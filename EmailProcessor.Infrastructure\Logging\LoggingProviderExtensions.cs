using System;
using System.Threading.Tasks;
using EmailProcessor.Domain.Interfaces;
using EmailProcessor.Domain.ValueObjects;

namespace EmailProcessor.Infrastructure.Logging
{
    /// <summary>
    /// Extension methods for ILoggingProvider to provide synchronous logging methods
    /// </summary>
    public static class LoggingProviderExtensions
    {
        /// <summary>
        /// Logs a debug message synchronously
        /// </summary>
        public static void LogDebug(this ILoggingProvider loggingProvider, string message, string sourceComponent = null, Guid? correlationId = null)
        {
            if (loggingProvider == null) return;
            
            Task.Run(async () => await loggingProvider.LogDebugAsync(message, sourceComponent ?? GetCallerTypeName(), correlationId));
        }

        /// <summary>
        /// Logs an information message synchronously
        /// </summary>
        public static void LogInformation(this ILoggingProvider loggingProvider, string message, string sourceComponent = null, Guid? correlationId = null)
        {
            if (loggingProvider == null) return;
            
            Task.Run(async () => await loggingProvider.LogInformationAsync(message, sourceComponent ?? GetCallerTypeName(), correlationId));
        }

        /// <summary>
        /// Logs a warning message synchronously
        /// </summary>
        public static void LogWarning(this ILoggingProvider loggingProvider, string message, string sourceComponent = null, Guid? correlationId = null)
        {
            if (loggingProvider == null) return;
            
            Task.Run(async () => await loggingProvider.LogWarningAsync(message, sourceComponent ?? GetCallerTypeName(), correlationId));
        }

        /// <summary>
        /// Logs an error message synchronously
        /// </summary>
        public static void LogError(this ILoggingProvider loggingProvider, string message, Exception exception = null, string sourceComponent = null, Guid? correlationId = null)
        {
            if (loggingProvider == null) return;
            
            Task.Run(async () => await loggingProvider.LogErrorAsync(message, sourceComponent ?? GetCallerTypeName(), exception, correlationId));
        }

        /// <summary>
        /// Logs a fatal message synchronously
        /// </summary>
        public static void LogFatal(this ILoggingProvider loggingProvider, string message, Exception exception = null, string sourceComponent = null, Guid? correlationId = null)
        {
            if (loggingProvider == null) return;
            
            Task.Run(async () => await loggingProvider.LogFatalAsync(message, sourceComponent ?? GetCallerTypeName(), exception, correlationId));
        }

        /// <summary>
        /// Gets the caller type name for automatic source component detection
        /// </summary>
        private static string GetCallerTypeName()
        {
            try
            {
                var stackTrace = new System.Diagnostics.StackTrace();
                var frame = stackTrace.GetFrame(3); // Go up the call stack to find the actual caller
                return frame?.GetMethod()?.DeclaringType?.Name ?? "Unknown";
            }
            catch
            {
                return "Unknown";
            }
        }
    }
}