using System;
using EmailProcessor.Domain.ValueObjects;
using EmailProcessor.Domain.Events;

namespace EmailProcessor.Domain.Entities
{
    /// <summary>
    /// Core Attachment entity representing an email attachment in the domain
    /// </summary>
    public class Attachment
    {
        public long AttachmentId { get; private set; }
        public long EmailId { get; private set; }
        public string FileName { get; private set; }
        public string OriginalFileName { get; private set; }
        public string ContentType { get; private set; }
        public string FileExtension { get; private set; }
        public long FileSize { get; private set; }
        public FilePath LocalStoragePath { get; private set; }
        public FilePath UncStoragePath { get; private set; }
        public ProcessingStatus LocalProcessingStatus { get; private set; }
        public ProcessingStatus UncProcessingStatus { get; private set; }
        public string LocalErrorMessage { get; private set; }
        public string UncErrorMessage { get; private set; }
        public DateTime CreatedDate { get; private set; }
        public DateTime ModifiedDate { get; private set; }
        
        // Navigation property for the parent email
        public virtual Email Email { get; private set; }
        
        // Navigation property for processing logs
        public virtual ICollection<ProcessingLog> ProcessingLogs { get; private set; }

        // Private constructor for EF Core
        private Attachment() { }

        public Attachment(
            long emailId,
            string fileName,
            string originalFileName,
            string contentType,
            string fileExtension,
            long fileSize)
        {
            ValidateAttachment(emailId, fileName, originalFileName, fileSize);
            
            EmailId = emailId;
            FileName = fileName;
            OriginalFileName = originalFileName;
            ContentType = contentType;
            FileExtension = fileExtension;
            FileSize = fileSize;
            LocalProcessingStatus = ProcessingStatus.Pending;
            UncProcessingStatus = ProcessingStatus.Pending;
            CreatedDate = DateTime.UtcNow;
            ModifiedDate = DateTime.UtcNow;
            
            ProcessingLogs = new List<ProcessingLog>();
            
            // Raise domain event
            AddDomainEvent(new AttachmentCreatedEvent(this));
        }

        public void SetLocalStoragePath(FilePath localPath)
        {
            LocalStoragePath = localPath ?? throw new ArgumentNullException(nameof(localPath));
            ModifiedDate = DateTime.UtcNow;
        }

        public void SetUncStoragePath(FilePath uncPath)
        {
            UncStoragePath = uncPath ?? throw new ArgumentNullException(nameof(uncPath));
            ModifiedDate = DateTime.UtcNow;
        }

        public void UpdateLocalProcessingStatus(ProcessingStatus status, string errorMessage = null)
        {
            if (LocalProcessingStatus == status)
                return;
                
            LocalProcessingStatus = status;
            LocalErrorMessage = errorMessage;
            ModifiedDate = DateTime.UtcNow;
            
            AddDomainEvent(new AttachmentLocalProcessingStatusChangedEvent(this, status));
        }

        public void UpdateUncProcessingStatus(ProcessingStatus status, string errorMessage = null)
        {
            if (UncProcessingStatus == status)
                return;
                
            UncProcessingStatus = status;
            UncErrorMessage = errorMessage;
            ModifiedDate = DateTime.UtcNow;
            
            AddDomainEvent(new AttachmentUncProcessingStatusChangedEvent(this, status));
        }

        public void AddProcessingLog(ProcessingLog log)
        {
            if (log == null)
                throw new ArgumentNullException(nameof(log));
                
            ProcessingLogs.Add(log);
        }

        public bool IsFullyProcessed => 
            LocalProcessingStatus == ProcessingStatus.Completed && 
            UncProcessingStatus == ProcessingStatus.Completed;

        public bool HasProcessingErrors =>
            LocalProcessingStatus == ProcessingStatus.Failed || 
            UncProcessingStatus == ProcessingStatus.Failed;

        private void ValidateAttachment(long emailId, string fileName, string originalFileName, long fileSize)
        {
            if (emailId <= 0)
                throw new ArgumentException("Email ID must be greater than zero", nameof(emailId));
                
            if (string.IsNullOrWhiteSpace(fileName))
                throw new ArgumentException("File name cannot be null or empty", nameof(fileName));
                
            if (string.IsNullOrWhiteSpace(originalFileName))
                throw new ArgumentException("Original file name cannot be null or empty", nameof(originalFileName));
                
            if (fileSize < 0)
                throw new ArgumentException("File size cannot be negative", nameof(fileSize));
        }

        // Domain events
        private readonly List<IDomainEvent> _domainEvents = new List<IDomainEvent>();
        public IReadOnlyCollection<IDomainEvent> DomainEvents => _domainEvents.AsReadOnly();

        protected void AddDomainEvent(IDomainEvent domainEvent)
        {
            _domainEvents.Add(domainEvent);
        }

        public void ClearDomainEvents()
        {
            _domainEvents.Clear();
        }
    }
} 