using System;
using System.Collections.Generic;

namespace EmailProcessor.AddIn.Interfaces
{
    /// <summary>
    /// Enhanced interface for VSTO configuration management
    /// Phase 4: Added comprehensive configuration options and utility methods
    /// </summary>
    public interface IVSTOConfiguration
    {
        // Basic communication settings
        string NamedPipeName { get; }
        int ConnectionTimeoutSeconds { get; }
        int RetryCount { get; }
        int RetryDelaySeconds { get; }
        int MaxRetryDelaySeconds { get; }

        // File processing settings
        long MaxFileSizeBytes { get; }
        List<string> ExcludedFileExtensions { get; }
        List<string> AllowedFileExtensions { get; }
        bool ProcessInlineImages { get; }
        int MaxAttachmentCount { get; }

        // Email processing settings
        bool ProcessReceivedEmails { get; }
        bool ProcessSentEmails { get; }
        int MaxEmailAgeDays { get; }
        List<string> ExcludedSenders { get; }
        List<string> ExcludedDomains { get; }

        // Logging settings
        string LogFilePath { get; }
        string LogLevel { get; }
        bool EnableDebugLogging { get; }
        int MaxLogFileSizeMB { get; }

        // Performance settings
        int MaxConcurrentProcessing { get; }
        bool EnableAsyncProcessing { get; }
        int ProcessingTimeoutSeconds { get; }

        // Security settings
        bool ValidateFileSignatures { get; }
        bool BlockExecutableFiles { get; }
        List<string> BlockedFileTypes { get; }

        // Configuration management methods
        void LoadConfiguration();
        void SaveConfiguration();
        string GetValue(string key, string defaultValue = "");
        void SetValue(string key, string value);

        // Phase 4: Utility methods for validation and filtering
        bool IsFileExtensionAllowed(string fileExtension);
        bool IsSenderExcluded(string senderEmail);
        bool IsEmailTooOld(DateTime emailDate);
    }
} 