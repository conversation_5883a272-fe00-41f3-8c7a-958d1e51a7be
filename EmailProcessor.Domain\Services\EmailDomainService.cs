using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using EmailProcessor.Domain.Entities;
using EmailProcessor.Domain.Interfaces;
using EmailProcessor.Domain.ValueObjects;

namespace EmailProcessor.Domain.Services
{
    /// <summary>
    /// Domain service for email-related business logic
    /// </summary>
    public class EmailDomainService
    {
        private readonly IEmailRepository _emailRepository;
        private readonly IProcessingLogRepository _processingLogRepository;

        public EmailDomainService(
            IEmailRepository emailRepository,
            IProcessingLogRepository processingLogRepository)
        {
            _emailRepository = emailRepository ?? throw new ArgumentNullException(nameof(emailRepository));
            _processingLogRepository = processingLogRepository ?? throw new ArgumentNullException(nameof(processingLogRepository));
        }

        /// <summary>
        /// Creates a new email with validation
        /// </summary>
        public async Task<Email> CreateEmailAsync(
            string subject,
            string senderName,
            EmailAddress senderEmail,
            List<EmailAddress> recipientTo,
            List<EmailAddress> recipientCC,
            EmailType emailType,
            DateTime timestamp,
            string outlookMessageId,
            Guid? correlationId = null)
        {
            // Check if email already exists
            var existingEmail = await _emailRepository.GetByOutlookMessageIdAsync(outlookMessageId);
            if (existingEmail != null)
            {
                var log = ProcessingLog.CreateWarning(
                    $"Email with Outlook message ID '{outlookMessageId}' already exists",
                    "EmailDomainService",
                    correlationId);
                await _processingLogRepository.AddAsync(log);
                return existingEmail;
            }

            // Create new email
            var email = new Email(subject, senderName, senderEmail, recipientTo, recipientCC, emailType, timestamp, outlookMessageId);
            
            // Save email
            var savedEmail = await _emailRepository.AddAsync(email);
            
            // Log creation
            var creationLog = ProcessingLog.CreateInfo(
                $"Email created with ID {savedEmail.EmailId}",
                "EmailDomainService",
                correlationId,
                savedEmail.EmailId);
            await _processingLogRepository.AddAsync(creationLog);
            
            return savedEmail;
        }

        /// <summary>
        /// Updates email processing status
        /// </summary>
        public async Task UpdateEmailProcessingStatusAsync(long emailId, ProcessingStatus newStatus, Guid? correlationId = null)
        {
            var email = await _emailRepository.GetByIdAsync(emailId);
            if (email == null)
            {
                var log = ProcessingLog.CreateError(
                    $"Email with ID {emailId} not found",
                    "EmailDomainService",
                    null,
                    correlationId);
                await _processingLogRepository.AddAsync(log);
                throw new ArgumentException($"Email with ID {emailId} not found");
            }

            var oldStatus = email.ProcessingStatus;
            email.UpdateProcessingStatus(newStatus);
            
            await _emailRepository.UpdateAsync(email);
            
            // Log status change
            var statusChangeLog = ProcessingLog.CreateInfo(
                $"Email processing status changed from {oldStatus} to {newStatus}",
                "EmailDomainService",
                correlationId,
                emailId);
            await _processingLogRepository.AddAsync(statusChangeLog);
        }

        /// <summary>
        /// Gets emails by processing status
        /// </summary>
        public async Task<IEnumerable<Email>> GetEmailsByProcessingStatusAsync(ProcessingStatus status)
        {
            return await _emailRepository.GetByProcessingStatusAsync(status);
        }

        /// <summary>
        /// Gets emails by type
        /// </summary>
        public async Task<IEnumerable<Email>> GetEmailsByTypeAsync(EmailType emailType)
        {
            return await _emailRepository.GetByEmailTypeAsync(emailType);
        }

        /// <summary>
        /// Gets emails within a date range
        /// </summary>
        public async Task<IEnumerable<Email>> GetEmailsByDateRangeAsync(DateTime fromDate, DateTime toDate)
        {
            if (fromDate > toDate)
                throw new ArgumentException("From date cannot be greater than to date");

            return await _emailRepository.GetByDateRangeAsync(fromDate, toDate);
        }

        /// <summary>
        /// Gets processing statistics
        /// </summary>
        public async Task<EmailProcessingStatistics> GetProcessingStatisticsAsync()
        {
            var pendingCount = await _emailRepository.GetCountByProcessingStatusAsync(ProcessingStatus.Pending);
            var processingCount = await _emailRepository.GetCountByProcessingStatusAsync(ProcessingStatus.Processing);
            var completedCount = await _emailRepository.GetCountByProcessingStatusAsync(ProcessingStatus.Completed);
            var failedCount = await _emailRepository.GetCountByProcessingStatusAsync(ProcessingStatus.Failed);
            
            var receivedCount = await _emailRepository.GetCountByEmailTypeAsync(EmailType.Received);
            var sentCount = await _emailRepository.GetCountByEmailTypeAsync(EmailType.Sent);

            return new EmailProcessingStatistics
            {
                PendingCount = pendingCount,
                ProcessingCount = processingCount,
                CompletedCount = completedCount,
                FailedCount = failedCount,
                ReceivedCount = receivedCount,
                SentCount = sentCount,
                TotalCount = pendingCount + processingCount + completedCount + failedCount
            };
        }

        /// <summary>
        /// Validates email data
        /// </summary>
        public bool ValidateEmailData(string subject, EmailAddress senderEmail, DateTime timestamp, string outlookMessageId)
        {
            if (string.IsNullOrWhiteSpace(subject))
                return false;

            if (senderEmail == null)
                return false;

            if (timestamp == default)
                return false;

            if (string.IsNullOrWhiteSpace(outlookMessageId))
                return false;

            return true;
        }
    }

    /// <summary>
    /// Statistics for email processing
    /// </summary>
    public class EmailProcessingStatistics
    {
        public int PendingCount { get; set; }
        public int ProcessingCount { get; set; }
        public int CompletedCount { get; set; }
        public int FailedCount { get; set; }
        public int ReceivedCount { get; set; }
        public int SentCount { get; set; }
        public int TotalCount { get; set; }
    }
} 