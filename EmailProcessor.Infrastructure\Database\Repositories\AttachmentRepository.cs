using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using EmailProcessor.Domain.Entities;
using EmailProcessor.Domain.Interfaces;
using EmailProcessor.Domain.ValueObjects;
using EmailProcessor.Infrastructure.Database.Context;

namespace EmailProcessor.Infrastructure.Database.Repositories
{
    public class AttachmentRepository : IAttachmentRepository
    {
        private readonly EmailProcessorContext _context;

        public AttachmentRepository(EmailProcessorContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        public async Task<Attachment> AddAsync(Attachment attachment)
        {
            if (attachment == null)
                throw new ArgumentNullException(nameof(attachment));

            await _context.Attachments.AddAsync(attachment);
            await _context.SaveChangesAsync();
            return attachment;
        }

        public async Task<Attachment> UpdateAsync(Attachment attachment)
        {
            if (attachment == null)
                throw new ArgumentNullException(nameof(attachment));

            _context.Attachments.Update(attachment);
            await _context.SaveChangesAsync();
            return attachment;
        }

        public async Task<Attachment> GetByIdAsync(long attachmentId)
        {
            return await _context.Attachments
                .Include(a => a.ProcessingLogs)
                .FirstOrDefaultAsync(a => a.AttachmentId == attachmentId);
        }

        public async Task<IEnumerable<Attachment>> GetByEmailIdAsync(long emailId)
        {
            return await _context.Attachments
                .Include(a => a.ProcessingLogs)
                .Where(a => a.EmailId == emailId)
                .OrderBy(a => a.FileName)
                .ToListAsync();
        }

        public async Task<IEnumerable<Attachment>> GetByProcessingStatusAsync(ProcessingStatus status)
        {
            return await _context.Attachments
                .Include(a => a.ProcessingLogs)
                .Where(a => a.LocalProcessingStatus == status || a.UncProcessingStatus == status)
                .OrderByDescending(a => a.CreatedDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Attachment>> GetByLocalProcessingStatusAsync(ProcessingStatus status)
        {
            return await _context.Attachments
                .Include(a => a.ProcessingLogs)
                .Where(a => a.LocalProcessingStatus == status)
                .OrderByDescending(a => a.CreatedDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Attachment>> GetByUncProcessingStatusAsync(ProcessingStatus status)
        {
            return await _context.Attachments
                .Include(a => a.ProcessingLogs)
                .Where(a => a.UncProcessingStatus == status)
                .OrderByDescending(a => a.CreatedDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Attachment>> GetByFileExtensionAsync(string fileExtension)
        {
            if (string.IsNullOrWhiteSpace(fileExtension))
                throw new ArgumentException("FileExtension cannot be null or empty", nameof(fileExtension));

            return await _context.Attachments
                .Include(a => a.ProcessingLogs)
                .Where(a => a.FileExtension.ToLower() == fileExtension.ToLower())
                .OrderByDescending(a => a.CreatedDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Attachment>> GetByFileSizeRangeAsync(long minSize, long maxSize)
        {
            if (minSize < 0 || maxSize < 0)
                throw new ArgumentException("File sizes cannot be negative");

            if (minSize > maxSize)
                throw new ArgumentException("MinSize cannot be greater than MaxSize");

            return await _context.Attachments
                .Include(a => a.ProcessingLogs)
                .Where(a => a.FileSize >= minSize && a.FileSize <= maxSize)
                .OrderByDescending(a => a.FileSize)
                .ToListAsync();
        }

        public async Task<IEnumerable<Attachment>> GetByContentTypeAsync(string contentType)
        {
            if (string.IsNullOrWhiteSpace(contentType))
                throw new ArgumentException("ContentType cannot be null or empty", nameof(contentType));

            return await _context.Attachments
                .Include(a => a.ProcessingLogs)
                .Where(a => a.ContentType.ToLower().Contains(contentType.ToLower()))
                .OrderByDescending(a => a.CreatedDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Attachment>> GetFailedAttachmentsAsync()
        {
            return await _context.Attachments
                .Include(a => a.ProcessingLogs)
                .Where(a => a.LocalProcessingStatus == ProcessingStatus.Failed || 
                           a.UncProcessingStatus == ProcessingStatus.Failed)
                .OrderByDescending(a => a.CreatedDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Attachment>> GetPendingAttachmentsAsync()
        {
            return await _context.Attachments
                .Include(a => a.ProcessingLogs)
                .Where(a => a.LocalProcessingStatus == ProcessingStatus.Pending || 
                           a.UncProcessingStatus == ProcessingStatus.Pending)
                .OrderBy(a => a.CreatedDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Attachment>> GetFullyProcessedAsync()
        {
            return await _context.Attachments
                .Include(a => a.ProcessingLogs)
                .Where(a => a.LocalProcessingStatus == ProcessingStatus.Completed && 
                           a.UncProcessingStatus == ProcessingStatus.Completed)
                .OrderByDescending(a => a.CreatedDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Attachment>> GetWithProcessingErrorsAsync()
        {
            return await _context.Attachments
                .Include(a => a.ProcessingLogs)
                .Where(a => !string.IsNullOrEmpty(a.LocalErrorMessage) || 
                           !string.IsNullOrEmpty(a.UncErrorMessage))
                .OrderByDescending(a => a.CreatedDate)
                .ToListAsync();
        }

        public async Task DeleteAsync(long attachmentId)
        {
            var attachment = await GetByIdAsync(attachmentId);
            if (attachment != null)
            {
                _context.Attachments.Remove(attachment);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<int> GetCountByProcessingStatusAsync(ProcessingStatus status)
        {
            return await _context.Attachments.CountAsync(a => 
                a.LocalProcessingStatus == status || a.UncProcessingStatus == status);
        }

        public async Task<int> GetCountByFileExtensionAsync(string fileExtension)
        {
            if (string.IsNullOrWhiteSpace(fileExtension))
                throw new ArgumentException("FileExtension cannot be null or empty", nameof(fileExtension));

            return await _context.Attachments.CountAsync(a => a.FileExtension.ToLower() == fileExtension.ToLower());
        }

        public async Task<long> GetTotalSizeAsync()
        {
            return await _context.Attachments.SumAsync(a => a.FileSize);
        }

        public async Task<long> GetTotalSizeByEmailIdAsync(long emailId)
        {
            return await _context.Attachments
                .Where(a => a.EmailId == emailId)
                .SumAsync(a => a.FileSize);
        }
    }
} 