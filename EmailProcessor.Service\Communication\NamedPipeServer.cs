using System.IO.Pipes;
using System.Text;
using System.Text.Json;
using EmailProcessor.Domain.Interfaces;
using EmailProcessor.Service.Models;

namespace EmailProcessor.Service.Communication
{
    /// <summary>
    /// Named Pipe server for communication with VSTO add-in
    /// </summary>
    public class NamedPipeServer : IDisposable
    {
        private readonly string _pipeName;
        private readonly ILoggingProvider _loggingProvider;
        private readonly List<NamedPipeServerStream> _activeConnections;
        private readonly object _connectionsLock = new object();
        private bool _isRunning;
        private bool _disposed;

        public event Func<EmailProcessingRequest, Task>? OnEmailProcessingRequest;

        public bool IsRunning => _isRunning;

        public NamedPipeServer(string pipeName, ILoggingProvider loggingProvider)
        {
            _pipeName = pipeName ?? throw new ArgumentNullException(nameof(pipeName));
            _loggingProvider = loggingProvider ?? throw new ArgumentNullException(nameof(loggingProvider));
            _activeConnections = new List<NamedPipeServerStream>();
        }

        public async Task StartAsync()
        {
            if (_isRunning)
            {
                await _loggingProvider.LogWarningAsync("Named Pipe server is already running", "NamedPipeServer");
                return;
            }

            try
            {
                _isRunning = true;
                await _loggingProvider.LogInformationAsync($"Starting Named Pipe server: {_pipeName}", "NamedPipeServer");

                // Start listening for connections
                _ = Task.Run(ListenForConnectionsAsync);

                await _loggingProvider.LogInformationAsync("Named Pipe server started successfully", "NamedPipeServer");
            }
            catch (Exception ex)
            {
                _isRunning = false;
                await _loggingProvider.LogErrorAsync("Failed to start Named Pipe server", "NamedPipeServer", ex);
                throw;
            }
        }

        public async Task StopAsync()
        {
            if (!_isRunning)
            {
                return;
            }

            try
            {
                _isRunning = false;
                await _loggingProvider.LogInformationAsync("Stopping Named Pipe server", "NamedPipeServer");

                // Close all active connections
                lock (_connectionsLock)
                {
                    foreach (var connection in _activeConnections.ToList())
                    {
                        try
                        {
                            connection.Close();
                            connection.Dispose();
                        }
                        catch (Exception ex)
                        {
                            _loggingProvider.LogErrorAsync("Error closing Named Pipe connection", "NamedPipeServer", ex).Wait();
                        }
                    }
                    _activeConnections.Clear();
                }

                await _loggingProvider.LogInformationAsync("Named Pipe server stopped successfully", "NamedPipeServer");
            }
            catch (Exception ex)
            {
                await _loggingProvider.LogErrorAsync("Error stopping Named Pipe server", "NamedPipeServer", ex);
                throw;
            }
        }

        private async Task ListenForConnectionsAsync()
        {
            while (_isRunning)
            {
                NamedPipeServerStream? pipeServer = null;
                try
                {
                    // Create a new pipe server instance
                    pipeServer = new NamedPipeServerStream(
                        _pipeName,
                        PipeDirection.InOut,
                        NamedPipeServerStream.MaxAllowedServerInstances,
                        PipeTransmissionMode.Message,
                        PipeOptions.Asynchronous);

                    await _loggingProvider.LogDebugAsync("Waiting for client connection", "NamedPipeServer");

                    // Wait for a client to connect
                    await pipeServer.WaitForConnectionAsync();

                    var correlationId = Guid.NewGuid();
                    await _loggingProvider.LogInformationAsync($"Client connected (CorrelationId: {correlationId})", "NamedPipeServer", correlationId);

                    // Add to active connections
                    lock (_connectionsLock)
                    {
                        _activeConnections.Add(pipeServer);
                    }

                    // Handle the client connection
                    _ = Task.Run(() => HandleClientConnectionAsync(pipeServer, correlationId));
                }
                catch (Exception ex)
                {
                    await _loggingProvider.LogErrorAsync("Error in connection listener", "NamedPipeServer", ex);
                    
                    // Clean up the pipe server if it was created
                    if (pipeServer != null)
                    {
                        try
                        {
                            pipeServer.Close();
                            pipeServer.Dispose();
                        }
                        catch { }
                    }
                }
            }
        }

        private async Task HandleClientConnectionAsync(NamedPipeServerStream pipeServer, Guid correlationId)
        {
            try
            {
                await _loggingProvider.LogDebugAsync($"Starting to handle client connection (CorrelationId: {correlationId})", "NamedPipeServer", correlationId);

                var buffer = new byte[4096];
                var messageBuilder = new StringBuilder();

                while (pipeServer.IsConnected && _isRunning)
                {
                    try
                    {
                        // Read message from pipe
                        var bytesRead = await pipeServer.ReadAsync(buffer, 0, buffer.Length);
                        
                        if (bytesRead == 0)
                        {
                            // Client disconnected
                            break;
                        }

                        // Convert bytes to string
                        var messageChunk = Encoding.UTF8.GetString(buffer, 0, bytesRead);
                        messageBuilder.Append(messageChunk);

                        // Check if we have a complete message
                        var message = messageBuilder.ToString();
                        if (IsCompleteMessage(message))
                        {
                            await ProcessMessageAsync(message, correlationId);
                            messageBuilder.Clear();
                        }
                    }
                    catch (Exception ex)
                    {
                        await _loggingProvider.LogErrorAsync($"Error reading from pipe (CorrelationId: {correlationId})", "NamedPipeServer", ex, correlationId);
                        break;
                    }
                }

                await _loggingProvider.LogInformationAsync($"Client connection ended (CorrelationId: {correlationId})", "NamedPipeServer", correlationId);
            }
            catch (Exception ex)
            {
                await _loggingProvider.LogErrorAsync($"Error handling client connection (CorrelationId: {correlationId})", "NamedPipeServer", ex, correlationId);
            }
            finally
            {
                // Remove from active connections and dispose
                lock (_connectionsLock)
                {
                    _activeConnections.Remove(pipeServer);
                }

                try
                {
                    pipeServer.Close();
                    pipeServer.Dispose();
                }
                catch (Exception ex)
                {
                    await _loggingProvider.LogErrorAsync("Error closing pipe server", "NamedPipeServer", ex, correlationId);
                }
            }
        }

        private async Task ProcessMessageAsync(string message, Guid correlationId)
        {
            try
            {
                await _loggingProvider.LogDebugAsync($"Processing message (CorrelationId: {correlationId}): {message.Substring(0, Math.Min(100, message.Length))}...", "NamedPipeServer", correlationId);

                // Deserialize the message
                var request = JsonSerializer.Deserialize<EmailProcessingRequest>(message, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (request == null)
                {
                    await _loggingProvider.LogErrorAsync($"Failed to deserialize message (CorrelationId: {correlationId})", "NamedPipeServer", null, correlationId);
                    return;
                }

                // Validate the request
                if (!IsValidRequest(request))
                {
                    await _loggingProvider.LogWarningAsync($"Invalid request received (CorrelationId: {correlationId})", "NamedPipeServer", correlationId);
                    return;
                }

                // Raise the event for processing
                if (OnEmailProcessingRequest != null)
                {
                    await OnEmailProcessingRequest(request);
                }

                await _loggingProvider.LogInformationAsync($"Message processed successfully (CorrelationId: {correlationId})", "NamedPipeServer", correlationId);
            }
            catch (JsonException ex)
            {
                await _loggingProvider.LogErrorAsync($"JSON deserialization error (CorrelationId: {correlationId})", "NamedPipeServer", ex, correlationId);
            }
            catch (Exception ex)
            {
                await _loggingProvider.LogErrorAsync($"Error processing message (CorrelationId: {correlationId})", "NamedPipeServer", ex, correlationId);
            }
        }

        private static bool IsCompleteMessage(string message)
        {
            // Simple check for complete JSON message
            // In a production system, you might want more sophisticated message framing
            return message.Trim().StartsWith("{") && message.Trim().EndsWith("}");
        }

        private static bool IsValidRequest(EmailProcessingRequest request)
        {
            return request != null &&
                   request.Data != null &&
                   !string.IsNullOrWhiteSpace(request.Data.Email?.Subject) &&
                   !string.IsNullOrWhiteSpace(request.Data.Email?.OutlookMessageId) &&
                   request.Data.Email?.Timestamp != default;
        }

        public async Task SendResponseAsync(EmailProcessingResponse response)
        {
            try
            {
                var responseJson = JsonSerializer.Serialize(response, new JsonSerializerOptions
                {
                    WriteIndented = false
                });

                var responseBytes = Encoding.UTF8.GetBytes(responseJson);

                // Send to all connected clients
                lock (_connectionsLock)
                {
                    foreach (var connection in _activeConnections.ToList())
                    {
                        try
                        {
                            if (connection.IsConnected)
                            {
                                connection.WriteAsync(responseBytes, 0, responseBytes.Length).Wait();
                            }
                        }
                        catch (Exception ex)
                        {
                            _loggingProvider.LogErrorAsync("Error sending response to client", "NamedPipeServer", ex).Wait();
                        }
                    }
                }

                await _loggingProvider.LogDebugAsync($"Response sent to {_activeConnections.Count} clients", "NamedPipeServer");
            }
            catch (Exception ex)
            {
                await _loggingProvider.LogErrorAsync("Error sending response", "NamedPipeServer", ex);
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                StopAsync().Wait();
                _disposed = true;
            }
        }
    }
} 