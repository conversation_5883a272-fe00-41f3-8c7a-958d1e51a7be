using System;
using System.IO;
using System.Threading.Tasks;
using EmailProcessor.Domain.ValueObjects;

namespace EmailProcessor.Domain.Interfaces
{
    /// <summary>
    /// Interface for storage providers (Local and UNC)
    /// </summary>
    public interface IStorageProvider
    {
        /// <summary>
        /// Gets the storage type (Local or UNC)
        /// </summary>
        StorageType StorageType { get; }

        /// <summary>
        /// Gets the base path for this storage provider
        /// </summary>
        string BasePath { get; }

        /// <summary>
        /// Saves a file to storage
        /// </summary>
        Task<FilePath> SaveFileAsync(byte[] fileData, string fileName, string directoryPath);

        /// <summary>
        /// Saves a file from stream to storage
        /// </summary>
        Task<FilePath> SaveFileAsync(Stream fileStream, string fileName, string directoryPath);

        /// <summary>
        /// Creates a directory if it doesn't exist
        /// </summary>
        Task CreateDirectoryAsync(string directoryPath);

        /// <summary>
        /// Checks if a file exists
        /// </summary>
        Task<bool> FileExistsAsync(string filePath);

        /// <summary>
        /// Checks if a directory exists
        /// </summary>
        Task<bool> DirectoryExistsAsync(string directoryPath);

        /// <summary>
        /// Deletes a file
        /// </summary>
        Task DeleteFileAsync(string filePath);

        /// <summary>
        /// Gets file information
        /// </summary>
        Task<FileInfo> GetFileInfoAsync(string filePath);

        /// <summary>
        /// Gets available disk space
        /// </summary>
        Task<long> GetAvailableSpaceAsync();

        /// <summary>
        /// Tests connectivity to the storage
        /// </summary>
        Task<bool> TestConnectivityAsync();
    }

    /// <summary>
    /// Enum representing storage types
    /// </summary>
    public enum StorageType
    {
        /// <summary>
        /// Local file system storage
        /// </summary>
        Local = 1,

        /// <summary>
        /// UNC network storage
        /// </summary>
        Unc = 2
    }
} 