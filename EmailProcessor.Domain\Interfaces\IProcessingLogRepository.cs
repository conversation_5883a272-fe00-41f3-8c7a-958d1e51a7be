using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EmailProcessor.Domain.Entities;
using EmailProcessor.Domain.ValueObjects;

namespace EmailProcessor.Domain.Interfaces
{
    /// <summary>
    /// Repository interface for ProcessingLog entities
    /// </summary>
    public interface IProcessingLogRepository
    {
        /// <summary>
        /// Gets a processing log by its ID
        /// </summary>
        Task<ProcessingLog> GetByIdAsync(long logId);

        /// <summary>
        /// Gets all processing logs for a specific email
        /// </summary>
        Task<IEnumerable<ProcessingLog>> GetByEmailIdAsync(long emailId);

        /// <summary>
        /// Gets all processing logs for a specific attachment
        /// </summary>
        Task<IEnumerable<ProcessingLog>> GetByAttachmentIdAsync(long attachmentId);

        /// <summary>
        /// Gets processing logs by log level
        /// </summary>
        Task<IEnumerable<ProcessingLog>> GetByLogLevelAsync(LogLevel logLevel);

        /// <summary>
        /// Gets processing logs by correlation ID
        /// </summary>
        Task<IEnumerable<ProcessingLog>> GetByCorrelationIdAsync(Guid correlationId);

        /// <summary>
        /// Gets processing logs within a date range
        /// </summary>
        Task<IEnumerable<ProcessingLog>> GetByDateRangeAsync(DateTime fromDate, DateTime toDate);

        /// <summary>
        /// Gets processing logs by source component
        /// </summary>
        Task<IEnumerable<ProcessingLog>> GetBySourceComponentAsync(string sourceComponent);

        /// <summary>
        /// Gets error logs
        /// </summary>
        Task<IEnumerable<ProcessingLog>> GetErrorLogsAsync();

        /// <summary>
        /// Gets warning logs
        /// </summary>
        Task<IEnumerable<ProcessingLog>> GetWarningLogsAsync();

        /// <summary>
        /// Adds a new processing log
        /// </summary>
        Task<ProcessingLog> AddAsync(ProcessingLog log);

        /// <summary>
        /// Adds multiple processing logs
        /// </summary>
        Task AddRangeAsync(IEnumerable<ProcessingLog> logs);

        /// <summary>
        /// Deletes processing logs older than the specified date
        /// </summary>
        Task<int> DeleteOlderThanAsync(DateTime date);

        /// <summary>
        /// Gets the count of logs by log level
        /// </summary>
        Task<int> GetCountByLogLevelAsync(LogLevel logLevel);

        /// <summary>
        /// Gets the count of logs for a specific email
        /// </summary>
        Task<int> GetCountByEmailIdAsync(long emailId);

        /// <summary>
        /// Gets the count of logs for a specific attachment
        /// </summary>
        Task<int> GetCountByAttachmentIdAsync(long attachmentId);
    }
} 