using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using EmailProcessor.Domain.Entities;
using EmailProcessor.Domain.Interfaces;
using EmailProcessor.Domain.ValueObjects;

namespace EmailProcessor.Domain.Services
{
    /// <summary>
    /// Domain service for attachment-related business logic
    /// </summary>
    public class AttachmentDomainService
    {
        private readonly IAttachmentRepository _attachmentRepository;
        private readonly IProcessingLogRepository _processingLogRepository;

        public AttachmentDomainService(
            IAttachmentRepository attachmentRepository,
            IProcessingLogRepository processingLogRepository)
        {
            _attachmentRepository = attachmentRepository ?? throw new ArgumentNullException(nameof(attachmentRepository));
            _processingLogRepository = processingLogRepository ?? throw new ArgumentNullException(nameof(processingLogRepository));
        }

        /// <summary>
        /// Creates a new attachment with validation
        /// </summary>
        public async Task<Attachment> CreateAttachmentAsync(
            long emailId,
            string fileName,
            string originalFileName,
            string contentType,
            string fileExtension,
            long fileSize,
            Guid? correlationId = null)
        {
            // Validate attachment data
            if (!ValidateAttachmentData(emailId, fileName, originalFileName, fileSize))
            {
                var log = ProcessingLog.CreateError(
                    "Invalid attachment data provided",
                    "AttachmentDomainService",
                    null,
                    correlationId);
                await _processingLogRepository.AddAsync(log);
                throw new ArgumentException("Invalid attachment data provided");
            }

            // Create new attachment
            var attachment = new Attachment(emailId, fileName, originalFileName, contentType, fileExtension, fileSize);
            
            // Save attachment
            var savedAttachment = await _attachmentRepository.AddAsync(attachment);
            
            // Log creation
            var attachmentCreationLog = ProcessingLog.CreateInfo(
                $"Attachment created with ID {savedAttachment.AttachmentId} for email {emailId}",
                "AttachmentDomainService",
                correlationId,
                emailId,
                savedAttachment.AttachmentId);
            await _processingLogRepository.AddAsync(attachmentCreationLog);
            
            return savedAttachment;
        }

        /// <summary>
        /// Updates attachment local processing status
        /// </summary>
        public async Task UpdateLocalProcessingStatusAsync(long attachmentId, ProcessingStatus status, string errorMessage = null, Guid? correlationId = null)
        {
            var attachment = await _attachmentRepository.GetByIdAsync(attachmentId);
            if (attachment == null)
            {
                var log = ProcessingLog.CreateError(
                    $"Attachment with ID {attachmentId} not found",
                    "AttachmentDomainService",
                    null,
                    correlationId);
                await _processingLogRepository.AddAsync(log);
                throw new ArgumentException($"Attachment with ID {attachmentId} not found");
            }

            var oldStatus = attachment.LocalProcessingStatus;
            attachment.UpdateLocalProcessingStatus(status, errorMessage);
            
            await _attachmentRepository.UpdateAsync(attachment);
            
            // Log status change
            var localStatusLog = ProcessingLog.CreateInfo(
                $"Attachment local processing status changed from {oldStatus} to {status}",
                "AttachmentDomainService",
                correlationId,
                attachment.EmailId,
                attachmentId);
            await _processingLogRepository.AddAsync(localStatusLog);
        }

        /// <summary>
        /// Updates attachment UNC processing status
        /// </summary>
        public async Task UpdateUncProcessingStatusAsync(long attachmentId, ProcessingStatus status, string errorMessage = null, Guid? correlationId = null)
        {
            var attachment = await _attachmentRepository.GetByIdAsync(attachmentId);
            if (attachment == null)
            {
                var log = ProcessingLog.CreateError(
                    $"Attachment with ID {attachmentId} not found",
                    "AttachmentDomainService",
                    null,
                    correlationId);
                await _processingLogRepository.AddAsync(log);
                throw new ArgumentException($"Attachment with ID {attachmentId} not found");
            }

            var oldStatus = attachment.UncProcessingStatus;
            attachment.UpdateUncProcessingStatus(status, errorMessage);
            
            await _attachmentRepository.UpdateAsync(attachment);
            
            // Log status change
            var uncStatusLog = ProcessingLog.CreateInfo(
                $"Attachment UNC processing status changed from {oldStatus} to {status}",
                "AttachmentDomainService",
                correlationId,
                attachment.EmailId,
                attachmentId);
            await _processingLogRepository.AddAsync(uncStatusLog);
        }

        /// <summary>
        /// Sets attachment storage paths
        /// </summary>
        public async Task SetStoragePathsAsync(long attachmentId, FilePath localPath, FilePath uncPath, Guid? correlationId = null)
        {
            var attachment = await _attachmentRepository.GetByIdAsync(attachmentId);
            if (attachment == null)
            {
                var log = ProcessingLog.CreateError(
                    $"Attachment with ID {attachmentId} not found",
                    "AttachmentDomainService",
                    null,
                    correlationId);
                await _processingLogRepository.AddAsync(log);
                throw new ArgumentException($"Attachment with ID {attachmentId} not found");
            }

            attachment.SetLocalStoragePath(localPath);
            attachment.SetUncStoragePath(uncPath);
            
            await _attachmentRepository.UpdateAsync(attachment);
            
            // Log storage paths set
            var storagePathLog = ProcessingLog.CreateInfo(
                $"Storage paths set for attachment {attachmentId}",
                "AttachmentDomainService",
                correlationId,
                attachment.EmailId,
                attachmentId);
            await _processingLogRepository.AddAsync(storagePathLog);
        }

        /// <summary>
        /// Gets attachments by processing status
        /// </summary>
        public async Task<IEnumerable<Attachment>> GetAttachmentsByProcessingStatusAsync(ProcessingStatus status)
        {
            return await _attachmentRepository.GetByProcessingStatusAsync(status);
        }

        /// <summary>
        /// Gets attachments with processing errors
        /// </summary>
        public async Task<IEnumerable<Attachment>> GetAttachmentsWithErrorsAsync()
        {
            return await _attachmentRepository.GetWithProcessingErrorsAsync();
        }

        /// <summary>
        /// Gets fully processed attachments
        /// </summary>
        public async Task<IEnumerable<Attachment>> GetFullyProcessedAttachmentsAsync()
        {
            return await _attachmentRepository.GetFullyProcessedAsync();
        }

        /// <summary>
        /// Gets attachments by file extension
        /// </summary>
        public async Task<IEnumerable<Attachment>> GetAttachmentsByFileExtensionAsync(string fileExtension)
        {
            if (string.IsNullOrWhiteSpace(fileExtension))
                throw new ArgumentException("File extension cannot be null or empty");

            return await _attachmentRepository.GetByFileExtensionAsync(fileExtension);
        }

        /// <summary>
        /// Gets attachment processing statistics
        /// </summary>
        public async Task<AttachmentProcessingStatistics> GetProcessingStatisticsAsync()
        {
            var pendingLocalCount = await _attachmentRepository.GetByLocalProcessingStatusAsync(ProcessingStatus.Pending).ContinueWith(t => t.Result.Count());
            var processingLocalCount = await _attachmentRepository.GetByLocalProcessingStatusAsync(ProcessingStatus.Processing).ContinueWith(t => t.Result.Count());
            var completedLocalCount = await _attachmentRepository.GetByLocalProcessingStatusAsync(ProcessingStatus.Completed).ContinueWith(t => t.Result.Count());
            var failedLocalCount = await _attachmentRepository.GetByLocalProcessingStatusAsync(ProcessingStatus.Failed).ContinueWith(t => t.Result.Count());

            var pendingUncCount = await _attachmentRepository.GetByUncProcessingStatusAsync(ProcessingStatus.Pending).ContinueWith(t => t.Result.Count());
            var processingUncCount = await _attachmentRepository.GetByUncProcessingStatusAsync(ProcessingStatus.Processing).ContinueWith(t => t.Result.Count());
            var completedUncCount = await _attachmentRepository.GetByUncProcessingStatusAsync(ProcessingStatus.Completed).ContinueWith(t => t.Result.Count());
            var failedUncCount = await _attachmentRepository.GetByUncProcessingStatusAsync(ProcessingStatus.Failed).ContinueWith(t => t.Result.Count());

            var totalSize = await _attachmentRepository.GetTotalSizeAsync();

            return new AttachmentProcessingStatistics
            {
                LocalPendingCount = pendingLocalCount,
                LocalProcessingCount = processingLocalCount,
                LocalCompletedCount = completedLocalCount,
                LocalFailedCount = failedLocalCount,
                UncPendingCount = pendingUncCount,
                UncProcessingCount = processingUncCount,
                UncCompletedCount = completedUncCount,
                UncFailedCount = failedUncCount,
                TotalSize = totalSize
            };
        }

        /// <summary>
        /// Validates attachment data
        /// </summary>
        public bool ValidateAttachmentData(long emailId, string fileName, string originalFileName, long fileSize)
        {
            if (emailId <= 0)
                return false;

            if (string.IsNullOrWhiteSpace(fileName))
                return false;

            if (string.IsNullOrWhiteSpace(originalFileName))
                return false;

            if (fileSize < 0)
                return false;

            return true;
        }

        /// <summary>
        /// Validates file extension
        /// </summary>
        public bool IsValidFileExtension(string fileExtension, IEnumerable<string> allowedExtensions)
        {
            if (string.IsNullOrWhiteSpace(fileExtension))
                return false;

            if (allowedExtensions == null || !allowedExtensions.Any())
                return true; // If no restrictions, allow all

            return allowedExtensions.Any(ext => 
                string.Equals(ext, fileExtension, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// Validates file size
        /// </summary>
        public bool IsValidFileSize(long fileSize, long maxFileSize)
        {
            return fileSize >= 0 && fileSize <= maxFileSize;
        }
    }

    /// <summary>
    /// Statistics for attachment processing
    /// </summary>
    public class AttachmentProcessingStatistics
    {
        public int LocalPendingCount { get; set; }
        public int LocalProcessingCount { get; set; }
        public int LocalCompletedCount { get; set; }
        public int LocalFailedCount { get; set; }
        public int UncPendingCount { get; set; }
        public int UncProcessingCount { get; set; }
        public int UncCompletedCount { get; set; }
        public int UncFailedCount { get; set; }
        public long TotalSize { get; set; }
    }
} 