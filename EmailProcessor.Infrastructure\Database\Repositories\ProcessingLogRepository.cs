using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using EmailProcessor.Domain.Entities;
using EmailProcessor.Domain.Interfaces;
using EmailProcessor.Domain.ValueObjects;
using EmailProcessor.Infrastructure.Database.Context;

namespace EmailProcessor.Infrastructure.Database.Repositories
{
    public class ProcessingLogRepository : IProcessingLogRepository
    {
        private readonly EmailProcessorContext _context;

        public ProcessingLogRepository(EmailProcessorContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        public async Task<ProcessingLog> AddAsync(ProcessingLog log)
        {
            if (log == null)
                throw new ArgumentNullException(nameof(log));

            await _context.ProcessingLogs.AddAsync(log);
            await _context.SaveChangesAsync();
            return log;
        }

        public async Task AddRangeAsync(IEnumerable<ProcessingLog> logs)
        {
            if (logs == null)
                throw new ArgumentNullException(nameof(logs));

            await _context.ProcessingLogs.AddRangeAsync(logs);
            await _context.SaveChangesAsync();
        }

        public async Task<ProcessingLog> GetByIdAsync(long logId)
        {
            return await _context.ProcessingLogs
                .FirstOrDefaultAsync(l => l.LogId == logId);
        }

        public async Task<IEnumerable<ProcessingLog>> GetByEmailIdAsync(long emailId)
        {
            return await _context.ProcessingLogs
                .Where(l => l.EmailId == emailId)
                .OrderByDescending(l => l.CreatedDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<ProcessingLog>> GetByAttachmentIdAsync(long attachmentId)
        {
            return await _context.ProcessingLogs
                .Where(l => l.AttachmentId == attachmentId)
                .OrderByDescending(l => l.CreatedDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<ProcessingLog>> GetByLogLevelAsync(LogLevel logLevel)
        {
            return await _context.ProcessingLogs
                .Where(l => l.LogLevel == logLevel)
                .OrderByDescending(l => l.CreatedDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<ProcessingLog>> GetByCorrelationIdAsync(Guid correlationId)
        {
            return await _context.ProcessingLogs
                .Where(l => l.CorrelationId == correlationId)
                .OrderBy(l => l.CreatedDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<ProcessingLog>> GetBySourceComponentAsync(string sourceComponent)
        {
            if (string.IsNullOrWhiteSpace(sourceComponent))
                throw new ArgumentException("SourceComponent cannot be null or empty", nameof(sourceComponent));

            return await _context.ProcessingLogs
                .Where(l => l.SourceComponent == sourceComponent)
                .OrderByDescending(l => l.CreatedDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<ProcessingLog>> GetByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            if (startDate > endDate)
                throw new ArgumentException("StartDate cannot be greater than EndDate");

            return await _context.ProcessingLogs
                .Where(l => l.CreatedDate >= startDate && l.CreatedDate <= endDate)
                .OrderByDescending(l => l.CreatedDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<ProcessingLog>> GetErrorLogsAsync()
        {
            return await _context.ProcessingLogs
                .Where(l => l.LogLevel == LogLevel.Error || l.LogLevel == LogLevel.Fatal)
                .OrderByDescending(l => l.CreatedDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<ProcessingLog>> GetWarningLogsAsync()
        {
            return await _context.ProcessingLogs
                .Where(l => l.LogLevel == LogLevel.Warning)
                .OrderByDescending(l => l.CreatedDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<ProcessingLog>> GetSystemLogsAsync()
        {
            return await _context.ProcessingLogs
                .Where(l => l.EmailId == null && l.AttachmentId == null)
                .OrderByDescending(l => l.CreatedDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<ProcessingLog>> GetLogsWithExceptionsAsync()
        {
            return await _context.ProcessingLogs
                .Where(l => !string.IsNullOrEmpty(l.ExceptionDetails))
                .OrderByDescending(l => l.CreatedDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<ProcessingLog>> GetRecentLogsAsync(int count = 100)
        {
            if (count <= 0)
                throw new ArgumentException("Count must be greater than zero", nameof(count));

            return await _context.ProcessingLogs
                .OrderByDescending(l => l.CreatedDate)
                .Take(count)
                .ToListAsync();
        }

        public async Task<int> GetCountByLogLevelAsync(LogLevel logLevel)
        {
            return await _context.ProcessingLogs.CountAsync(l => l.LogLevel == logLevel);
        }

        public async Task<int> GetCountByEmailIdAsync(long emailId)
        {
            return await _context.ProcessingLogs.CountAsync(l => l.EmailId == emailId);
        }

        public async Task<int> GetCountByAttachmentIdAsync(long attachmentId)
        {
            return await _context.ProcessingLogs.CountAsync(l => l.AttachmentId == attachmentId);
        }

        public async Task<int> GetCountByCorrelationIdAsync(Guid correlationId)
        {
            return await _context.ProcessingLogs.CountAsync(l => l.CorrelationId == correlationId);
        }

        public async Task<int> GetErrorCountAsync(DateTime? since = null)
        {
            var query = _context.ProcessingLogs.Where(l => l.LogLevel == LogLevel.Error || l.LogLevel == LogLevel.Fatal);
            
            if (since.HasValue)
                query = query.Where(l => l.CreatedDate >= since.Value);

            return await query.CountAsync();
        }

        public async Task<IEnumerable<ProcessingLog>> GetLogsByMessagePatternAsync(string pattern)
        {
            if (string.IsNullOrWhiteSpace(pattern))
                throw new ArgumentException("Pattern cannot be null or empty", nameof(pattern));

            return await _context.ProcessingLogs
                .Where(l => l.Message.Contains(pattern))
                .OrderByDescending(l => l.CreatedDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<ProcessingLog>> GetLogsByExceptionTypeAsync(string exceptionType)
        {
            if (string.IsNullOrWhiteSpace(exceptionType))
                throw new ArgumentException("ExceptionType cannot be null or empty", nameof(exceptionType));

            return await _context.ProcessingLogs
                .Where(l => l.ExceptionDetails != null && l.ExceptionDetails.Contains(exceptionType))
                .OrderByDescending(l => l.CreatedDate)
                .ToListAsync();
        }

        public async Task<bool> ExistsAsync(long logId)
        {
            return await _context.ProcessingLogs.AnyAsync(l => l.LogId == logId);
        }

        public async Task<bool> HasErrorsForEmailAsync(long emailId)
        {
            return await _context.ProcessingLogs
                .AnyAsync(l => l.EmailId == emailId && 
                              (l.LogLevel == LogLevel.Error || l.LogLevel == LogLevel.Fatal));
        }

        public async Task<bool> HasErrorsForAttachmentAsync(long attachmentId)
        {
            return await _context.ProcessingLogs
                .AnyAsync(l => l.AttachmentId == attachmentId && 
                              (l.LogLevel == LogLevel.Error || l.LogLevel == LogLevel.Fatal));
        }

        public async Task<DateTime?> GetLastLogDateAsync()
        {
            return await _context.ProcessingLogs
                .MaxAsync(l => (DateTime?)l.CreatedDate);
        }

        public async Task<DateTime?> GetLastErrorDateAsync()
        {
            return await _context.ProcessingLogs
                .Where(l => l.LogLevel == LogLevel.Error || l.LogLevel == LogLevel.Fatal)
                .MaxAsync(l => (DateTime?)l.CreatedDate);
        }

        public async Task<int> DeleteOlderThanAsync(DateTime date)
        {
            var logsToDelete = await _context.ProcessingLogs
                .Where(l => l.CreatedDate < date)
                .ToListAsync();

            var count = logsToDelete.Count;
            _context.ProcessingLogs.RemoveRange(logsToDelete);
            await _context.SaveChangesAsync();
            return count;
        }
    }
} 