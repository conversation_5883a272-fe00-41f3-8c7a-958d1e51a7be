using System;

namespace EmailProcessor.Domain.Entities
{
    public class Configuration
    {
        public int ConfigId { get; private set; }
        public string ConfigKey { get; private set; }
        public string ConfigValue { get; private set; }
        public string Description { get; private set; }
        public bool IsEncrypted { get; private set; }
        public DateTime CreatedDate { get; private set; }
        public DateTime ModifiedDate { get; private set; }

        // Private constructor for EF Core
        private Configuration() { }

        public Configuration(string configKey, string configValue, string description = null, bool isEncrypted = false)
        {
            if (string.IsNullOrWhiteSpace(configKey))
                throw new ArgumentException("ConfigKey cannot be null or empty", nameof(configKey));

            if (configValue == null)
                throw new ArgumentException("ConfigValue cannot be null", nameof(configValue));

            ConfigKey = configKey.Trim();
            ConfigValue = configValue;
            Description = description?.Trim();
            IsEncrypted = isEncrypted;
            CreatedDate = DateTime.UtcNow;
            ModifiedDate = DateTime.UtcNow;
        }

        public void UpdateValue(string newValue)
        {
            if (newValue == null)
                throw new ArgumentException("ConfigValue cannot be null", nameof(newValue));

            ConfigValue = newValue;
            ModifiedDate = DateTime.UtcNow;
        }

        public void UpdateDescription(string newDescription)
        {
            Description = newDescription?.Trim();
            ModifiedDate = DateTime.UtcNow;
        }

        public void SetEncrypted(bool isEncrypted)
        {
            IsEncrypted = isEncrypted;
            ModifiedDate = DateTime.UtcNow;
        }
    }
} 