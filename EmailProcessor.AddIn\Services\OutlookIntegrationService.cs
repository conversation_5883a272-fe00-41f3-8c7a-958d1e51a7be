using System;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using System.Runtime.InteropServices;
using EmailProcessor.AddIn.Interfaces;
using EmailProcessor.AddIn.Models;
using EmailProcessor.Shared.Models;

namespace EmailProcessor.AddIn.Services
{
    /// <summary>
    /// Enhanced service for integrating with Outlook and extracting email/attachment data
    /// Phase 4: Enhanced with better error handling, metadata extraction, and VSTO safety
    /// </summary>
    public class OutlookIntegrationService : IOutlookIntegration
    {
        private readonly IVSTOConfiguration _configuration;
        private readonly object _extractionLock = new object();

        public OutlookIntegrationService(IVSTOConfiguration configuration)
        {
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        }

        public EmailProcessor.Shared.Models.EmailData ExtractEmailData(dynamic mailItem)
        {
            if (mailItem == null)
                throw new ArgumentNullException(nameof(mailItem));

            try
            {
                // Use lock to prevent concurrent access to COM objects
                lock (_extractionLock)
                {
                    var emailData = new EmailProcessor.Shared.Models.EmailData
                    {
                        Subject = GetPropertyValue(mailItem, "Subject") ?? "",
                        SenderName = GetPropertyValue(mailItem, "SenderName") ?? "",
                        SenderEmail = GetPropertyValue(mailItem, "SenderEmailAddress") ?? "",
                        EmailType = GetEmailType(mailItem),
                        Timestamp = GetDateTimeProperty(mailItem, "ReceivedTime"),
                        OutlookMessageId = GetPropertyValue(mailItem, "EntryID") ?? "",
                        EntryId = GetPropertyValue(mailItem, "EntryID") ?? "",
                        ConversationId = GetPropertyValue(mailItem, "ConversationID") ?? "",
                        HasAttachments = GetBoolProperty(mailItem, "Attachments.Count") > 0,
                        AttachmentCount = GetIntProperty(mailItem, "Attachments.Count"),
                        MessageSize = GetLongProperty(mailItem, "Size"),
                        IsRead = GetBoolProperty(mailItem, "UnRead") == false,
                        Importance = GetPropertyValue(mailItem, "Importance") ?? "Normal",
                        Categories = GetPropertyValue(mailItem, "Categories") ?? "",
                        Sensitivity = GetPropertyValue(mailItem, "Sensitivity") ?? "Normal"
                    };

                    // Extract recipients with enhanced error handling
                    emailData.RecipientTo = ExtractRecipients(mailItem, "To");
                    emailData.RecipientCC = ExtractRecipients(mailItem, "CC");
                    emailData.RecipientBCC = ExtractRecipients(mailItem, "BCC");

                    // Validate extracted data
                    ValidateEmailData(emailData);

                    return emailData;
                }
            }
            catch (COMException comEx)
            {
                // Handle COM-specific errors that could crash Outlook
                System.Diagnostics.Debug.WriteLine($"COM error extracting email data: {comEx.Message}");
                throw new InvalidOperationException($"COM error during email data extraction: {comEx.Message}", comEx);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error extracting email data: {ex.Message}");
                throw new InvalidOperationException($"Failed to extract email data: {ex.Message}", ex);
            }
        }

        public List<EmailProcessor.Shared.Models.AttachmentData> ExtractAttachmentData(dynamic mailItem, long maxFileSize = 100 * 1024 * 1024)
        {
            if (mailItem == null)
                throw new ArgumentNullException(nameof(mailItem));

            var attachments = new List<EmailProcessor.Shared.Models.AttachmentData>();

            try
            {
                lock (_extractionLock)
                {
                    var attachmentCount = GetIntProperty(mailItem, "Attachments.Count");
                    
                    for (int i = 1; i <= attachmentCount; i++)
                    {
                        try
                        {
                            var attachment = mailItem.Attachments[i];
                            
                            if (!ShouldProcessAttachment(attachment))
                                continue;

                            var attachmentData = ExtractSingleAttachment(attachment, maxFileSize);
                            if (attachmentData != null)
                            {
                                attachments.Add(attachmentData);
                            }
                        }
                        catch (COMException comEx)
                        {
                            // Handle COM-specific errors for individual attachments
                            System.Diagnostics.Debug.WriteLine($"COM error extracting attachment {i}: {comEx.Message}");
                            continue; // Skip this attachment and continue with others
                        }
                        catch (Exception ex)
                        {
                            // Log error but continue with other attachments
                            System.Diagnostics.Debug.WriteLine($"Error extracting attachment {i}: {ex.Message}");
                            continue;
                        }
                    }
                }
            }
            catch (COMException comEx)
            {
                System.Diagnostics.Debug.WriteLine($"COM error accessing attachments: {comEx.Message}");
                throw new InvalidOperationException($"COM error during attachment extraction: {comEx.Message}", comEx);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error extracting attachment data: {ex.Message}");
                throw new InvalidOperationException($"Failed to extract attachment data: {ex.Message}", ex);
            }

            return attachments;
        }

        public bool ShouldProcessEmail(dynamic mailItem)
        {
            if (mailItem == null)
                return false;

            try
            {
                // Check if email type should be processed
                var emailType = GetEmailType(mailItem);
                if (emailType == "Received" && !_configuration.ProcessReceivedEmails)
                    return false;
                if (emailType == "Sent" && !_configuration.ProcessSentEmails)
                    return false;

                // Check if email has attachments
                var hasAttachments = GetBoolProperty(mailItem, "Attachments.Count") > 0;
                if (!hasAttachments)
                    return false;

                // Check if email is too old (optional - could be configurable)
                var receivedTime = GetDateTimeProperty(mailItem, "ReceivedTime");
                if (receivedTime < DateTime.Now.AddDays(-30)) // Skip emails older than 30 days
                    return false;

                // Check if email is from excluded senders (could be configurable)
                var senderEmail = GetPropertyValue(mailItem, "SenderEmailAddress") ?? "";
                if (IsExcludedSender(senderEmail))
                    return false;

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error checking if email should be processed: {ex.Message}");
                return false; // Default to not processing if there's an error
            }
        }

        public bool ShouldProcessAttachment(dynamic attachment)
        {
            if (attachment == null)
                return false;

            try
            {
                // Check file size
                var fileSize = GetLongProperty(attachment, "Size");
                if (fileSize > _configuration.MaxFileSizeBytes)
                {
                    System.Diagnostics.Debug.WriteLine($"Attachment too large: {fileSize} bytes");
                    return false;
                }

                // Check file extension
                var fileName = GetPropertyValue(attachment, "FileName") ?? "";
                var fileExtension = Path.GetExtension(fileName).ToLowerInvariant();
                
                if (_configuration.ExcludedFileExtensions.Contains(fileExtension))
                {
                    System.Diagnostics.Debug.WriteLine($"Excluded file extension: {fileExtension}");
                    return false;
                }

                // Check if it's an embedded image (usually not worth processing)
                var contentType = GetPropertyValue(attachment, "ContentType") ?? "";
                if (contentType.StartsWith("image/") && fileSize < 1024 * 1024) // Small images
                {
                    System.Diagnostics.Debug.WriteLine($"Skipping small embedded image: {fileName}");
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error checking if attachment should be processed: {ex.Message}");
                return false;
            }
        }

        public string GetEmailType(dynamic mailItem)
        {
            try
            {
                // Check if it's a sent email by looking at the sender
                var senderEmail = GetPropertyValue(mailItem, "SenderEmailAddress") ?? "";
                var currentUserEmail = GetPropertyValue(mailItem, "Session.CurrentUser.Address") ?? "";
                
                if (!string.IsNullOrEmpty(senderEmail) && !string.IsNullOrEmpty(currentUserEmail))
                {
                    if (senderEmail.Equals(currentUserEmail, StringComparison.OrdinalIgnoreCase))
                        return "Sent";
                }

                return "Received";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error determining email type: {ex.Message}");
                return "Received"; // Default to received
            }
        }

        private EmailProcessor.Shared.Models.AttachmentData ExtractSingleAttachment(dynamic attachment, long maxFileSize)
        {
            try
            {
                var fileName = GetPropertyValue(attachment, "FileName") ?? "";
                var fileSize = GetLongProperty(attachment, "Size");
                var contentType = GetPropertyValue(attachment, "ContentType") ?? "";
                var fileExtension = Path.GetExtension(fileName).ToLowerInvariant();

                // Validate file size
                if (fileSize > maxFileSize)
                {
                    System.Diagnostics.Debug.WriteLine($"Attachment {fileName} exceeds size limit: {fileSize} bytes");
                    return null;
                }

                // Extract file data
                var fileData = ExtractAttachmentFileData(attachment, fileSize);
                if (fileData == null || fileData.Length == 0)
                {
                    System.Diagnostics.Debug.WriteLine($"Failed to extract data for attachment: {fileName}");
                    return null;
                }

                return new EmailProcessor.Shared.Models.AttachmentData
                {
                    FileName = fileName,
                    OriginalFileName = fileName,
                    ContentType = contentType,
                    FileExtension = fileExtension,
                    FileSize = fileSize,
                    FileData = Convert.ToBase64String(fileData)
                };
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error extracting single attachment: {ex.Message}");
                return null;
            }
        }

        private byte[] ExtractAttachmentFileData(dynamic attachment, long fileSize)
        {
            try
            {
                // Use temporary file to avoid memory issues with large attachments
                var tempFile = Path.GetTempFileName();
                
                try
                {
                    // Save attachment to temporary file
                    attachment.SaveAsFile(tempFile);
                    
                    // Read file data
                    if (File.Exists(tempFile))
                    {
                        var fileData = File.ReadAllBytes(tempFile);
                        return fileData;
                    }
                }
                finally
                {
                    // Clean up temporary file
                    try
                    {
                        if (File.Exists(tempFile))
                            File.Delete(tempFile);
                    }
                    catch
                    {
                        // Ignore cleanup errors
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error extracting attachment file data: {ex.Message}");
                return null;
            }
        }

        private List<string> ExtractRecipients(dynamic mailItem, string recipientType)
        {
            var recipients = new List<string>();

            try
            {
                var recipientCollection = GetPropertyValue(mailItem, $"Recipients") as dynamic;
                if (recipientCollection != null)
                {
                    var count = GetIntProperty(recipientCollection, "Count");
                    
                    for (int i = 1; i <= count; i++)
                    {
                        try
                        {
                            var recipient = recipientCollection[i];
                            var type = GetIntProperty(recipient, "Type");
                            
                            // Map recipient types: 1=To, 2=CC, 3=BCC
                            int expectedType;
                            switch (recipientType)
                            {
                                case "To":
                                    expectedType = 1;
                                    break;
                                case "CC":
                                    expectedType = 2;
                                    break;
                                case "BCC":
                                    expectedType = 3;
                                    break;
                                default:
                                    expectedType = 1;
                                    break;
                            }

                            if (type == expectedType)
                            {
                                var email = GetPropertyValue(recipient, "Address") ?? "";
                                var name = GetPropertyValue(recipient, "Name") ?? "";
                                
                                if (!string.IsNullOrEmpty(email))
                                {
                                    var recipientInfo = string.IsNullOrEmpty(name) ? email : $"{name} <{email}>";
                                    recipients.Add(recipientInfo);
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"Error extracting recipient {i}: {ex.Message}");
                            continue;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error extracting recipients: {ex.Message}");
            }

            return recipients;
        }

        private void ValidateEmailData(EmailProcessor.Shared.Models.EmailData emailData)
        {
            if (string.IsNullOrEmpty(emailData.Subject))
                emailData.Subject = "(No Subject)";

            if (string.IsNullOrEmpty(emailData.SenderEmail))
                throw new InvalidOperationException("Sender email address is required");

            if (emailData.Timestamp == DateTime.MinValue)
                emailData.Timestamp = DateTime.Now;
        }

        private bool IsExcludedSender(string senderEmail)
        {
            // Could be enhanced with configuration-based exclusion list
            if (string.IsNullOrEmpty(senderEmail))
                return false;

            // Example: exclude system emails
            var excludedDomains = new[] { "noreply", "no-reply", "donotreply", "do-not-reply" };
            return excludedDomains.Any(domain => senderEmail.ToLowerInvariant().Contains(domain));
        }

        private string GetPropertyValue(dynamic obj, string propertyName)
        {
            try
            {
                var property = obj.GetType().GetProperty(propertyName);
                if (property != null)
                {
                    var value = property.GetValue(obj);
                    return value != null ? value.ToString() : null;
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        private int GetIntProperty(dynamic obj, string propertyName)
        {
            try
            {
                var value = GetPropertyValue(obj, propertyName);
                int result;
                if (int.TryParse(value, out result))
                {
                    return result;
                }
                return 0;
            }
            catch
            {
                return 0;
            }
        }

        private long GetLongProperty(dynamic obj, string propertyName)
        {
            try
            {
                var value = GetPropertyValue(obj, propertyName);
                long result;
                if (long.TryParse(value, out result))
                {
                    return result;
                }
                return 0;
            }
            catch
            {
                return 0;
            }
        }

        private bool GetBoolProperty(dynamic obj, string propertyName)
        {
            try
            {
                var value = GetPropertyValue(obj, propertyName);
                bool result;
                if (bool.TryParse(value, out result))
                {
                    return result;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        private DateTime GetDateTimeProperty(dynamic obj, string propertyName)
        {
            try
            {
                var value = GetPropertyValue(obj, propertyName);
                DateTime result;
                if (DateTime.TryParse(value, out result))
                {
                    return result;
                }
                return DateTime.UtcNow;
            }
            catch
            {
                return DateTime.UtcNow;
            }
        }
    }
} 