using System;
using System.IO.Pipes;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using EmailProcessor.AddIn.Interfaces;
using EmailProcessor.AddIn.Models;

namespace EmailProcessor.AddIn.Communication
{
    /// <summary>
    /// Event arguments for connection errors
    /// </summary>
    public class ConnectionErrorEventArgs : EventArgs
    {
        public string ErrorMessage { get; }
        public Exception Exception { get; }
        public DateTime Timestamp { get; }

        public ConnectionErrorEventArgs(string errorMessage, Exception exception = null)
        {
            ErrorMessage = errorMessage ?? "Unknown connection error";
            Exception = exception;
            Timestamp = DateTime.UtcNow;
        }
    }

    /// <summary>
    /// Named Pipe client for communication with Windows Service
    /// </summary>
    public class NamedPipeClient : INamedPipeClient
    {
        private readonly string _pipeName;
        private NamedPipeClientStream _pipeClient;
        private bool _disposed;
        private readonly object _lockObject = new object();

        public bool IsConnected => _pipeClient != null && _pipeClient.IsConnected;

        public event EventHandler ConnectionLost;
        public event EventHandler Connected;
        public event EventHandler<ConnectionErrorEventArgs> ConnectionError;

        public NamedPipeClient(string pipeName)
        {
            _pipeName = pipeName ?? throw new ArgumentNullException(nameof(pipeName));
        }

        public async Task<bool> ConnectAsync(int timeoutSeconds = 10)
        {
            try
            {
                lock (_lockObject)
                {
                    if (_pipeClient != null && _pipeClient.IsConnected)
                    {
                        return true; // Already connected
                    }

                    // Dispose existing connection if any
                    if (_pipeClient != null)
                    {
                        _pipeClient.Dispose();
                    }

                    // Create new pipe client
                    _pipeClient = new NamedPipeClientStream(
                        ".", // Server name (local machine)
                        _pipeName,
                        PipeDirection.InOut,
                        PipeOptions.Asynchronous);
                }

                // Connect with timeout
                var connectTask = _pipeClient.ConnectAsync();
                var timeoutTask = Task.Delay(TimeSpan.FromSeconds(timeoutSeconds));

                var completedTask = await Task.WhenAny(connectTask, timeoutTask);
                if (completedTask == timeoutTask)
                {
                    // Timeout occurred
                    if (_pipeClient != null)
                    {
                        _pipeClient.Dispose();
                        _pipeClient = null;
                    }
                    OnConnectionError("Connection timeout", new TimeoutException("Connection timed out"));
                    return false;
                }

                // Wait for connection to complete
                await connectTask;

                if (_pipeClient != null && _pipeClient.IsConnected)
                {
                    OnConnected();
                    return true;
                }

                OnConnectionError("Failed to establish connection");
                return false;
            }
            catch (Exception ex)
            {
                if (_pipeClient != null)
                {
                    _pipeClient.Dispose();
                    _pipeClient = null;
                }
                OnConnectionError("Connection failed", ex);
                return false;
            }
        }

        public async Task DisconnectAsync()
        {
            try
            {
                lock (_lockObject)
                {
                    if (_pipeClient != null)
                    {
                        _pipeClient.Dispose();
                        _pipeClient = null;
                    }
                }
                OnConnectionLost();
            }
            catch (Exception ex)
            {
                OnConnectionError("Error during disconnection", ex);
            }
        }

        public async Task<ProcessingResponse> SendMessageAsync(ProcessingMessage message)
        {
            if (message == null)
                throw new ArgumentNullException(nameof(message));

            if (!IsConnected)
            {
                OnConnectionError("Cannot send message: not connected");
                throw new InvalidOperationException("Not connected to the service");
            }

            try
            {
                // Serialize the message
                var jsonMessage = JsonSerializer.Serialize(message);
                var messageBytes = Encoding.UTF8.GetBytes(jsonMessage);

                // Send the message
                await _pipeClient.WriteAsync(messageBytes, 0, messageBytes.Length);
                await _pipeClient.FlushAsync();

                // Read the response
                var response = await ReadResponseAsync();
                return response;
            }
            catch (Exception ex)
            {
                OnConnectionError("Error sending message", ex);
                throw;
            }
        }

        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                if (!IsConnected)
                {
                    return false;
                }

                // Send a simple test message
                var testMessage = new ProcessingMessage
                {
                    MessageType = "TestConnection",
                    CorrelationId = Guid.NewGuid(),
                    Timestamp = DateTime.UtcNow
                };

                var response = await SendMessageAsync(testMessage);
                return response != null && response.Success;
            }
            catch (Exception ex)
            {
                OnConnectionError("Test connection failed", ex);
                return false;
            }
        }

        private async Task<ProcessingResponse> ReadResponseAsync()
        {
            try
            {
                var buffer = new byte[4096];
                var responseBuilder = new StringBuilder();

                while (true)
                {
                    var bytesRead = await _pipeClient.ReadAsync(buffer, 0, buffer.Length);
                    if (bytesRead == 0)
                    {
                        // End of stream
                        break;
                    }

                    var chunk = Encoding.UTF8.GetString(buffer, 0, bytesRead);
                    responseBuilder.Append(chunk);

                    // Check if we have a complete JSON message
                    var response = responseBuilder.ToString();
                    if (IsCompleteMessage(response))
                    {
                        try
                        {
                            return JsonSerializer.Deserialize<ProcessingResponse>(response);
                        }
                        catch (JsonException ex)
                        {
                            OnConnectionError("Failed to deserialize response", ex);
                            throw;
                        }
                    }
                }

                OnConnectionError("Incomplete response received");
                throw new InvalidOperationException("Incomplete response received");
            }
            catch (Exception ex)
            {
                OnConnectionError("Error reading response", ex);
                throw;
            }
        }

        private static bool IsCompleteMessage(string message)
        {
            if (string.IsNullOrEmpty(message))
                return false;

            try
            {
                // Try to parse as JSON to see if it's complete
                JsonDocument.Parse(message);
                return true;
            }
            catch (JsonException)
            {
                return false;
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                _pipeClient?.Dispose();
                _disposed = true;
            }
        }

        private void OnConnectionLost()
        {
            ConnectionLost?.Invoke(this, EventArgs.Empty);
        }

        private void OnConnected()
        {
            Connected?.Invoke(this, EventArgs.Empty);
        }

        private void OnConnectionError(string errorMessage, Exception exception = null)
        {
            ConnectionError?.Invoke(this, new ConnectionErrorEventArgs(errorMessage, exception));
        }
    }
} 