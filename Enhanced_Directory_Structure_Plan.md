# 📁 Enhanced Email Directory Structure Plan

## 🎯 Executive Summary

This plan enhances the current directory structure from
`{Year}\{Month}\{Day}\{EmailType}` to include sender and recipient name
organization, creating a more intuitive and searchable folder hierarchy for
email attachments.

## 📊 Current Structure Analysis

### **Existing Pattern**

```
{Year}\{Month}\{Day}\{EmailType}
Example: 2024\12\15\Sent\
```

### **Current Limitations**

- ❌ No sender/recipient organization
- ❌ Difficult to find specific email attachments
- ❌ No logical grouping by communication relationships
- ❌ Limited searchability and organization

## 🏗️ New Directory Structure Design

### **Enhanced Structure Pattern**

#### **For Sent Emails**

```
{Year}\{Month}\{Day}\Sent\{PrimaryRecipientName}\{Attachments}
Example: 2024\12\15\Sent\John_Smith\document.pdf
```

#### **For Received Emails**

```
{Year}\{Month}\{Day}\Received\{SenderName}\{Attachments}
Example: 2024\12\15\Received\Jane_Doe\invoice.pdf
```

### **Multiple Recipients Handling**

#### **Strategy 1: Primary Recipient Only**

```
2024\12\15\Sent\PrimaryRecipient\attachment1.pdf
2024\12\15\Sent\PrimaryRecipient\attachment2.docx
```

#### **Strategy 2: Combined Recipients**

```
2024\12\15\Sent\PrimaryRecipient_and_SecondaryRecipient\attachment.pdf
```

#### **Strategy 3: Separate Folders**

```
2024\12\15\Sent\PrimaryRecipient\SecondaryRecipient\attachment.pdf
```

## 🔧 Implementation Plan

### **Phase 1: Database and Configuration Changes**

#### **1.1 Configuration Updates**

**File**:
`EmailProcessor.Infrastructure/Configuration/EmailProcessorConfiguration.cs`

```csharp
public class StorageConfiguration
{
    // Existing properties...

    /// <summary>
    /// Directory structure type (Basic or Enhanced)
    /// </summary>
    public DirectoryStructureType DirectoryStructureType { get; set; } = DirectoryStructureType.Basic;

    /// <summary>
    /// Name sanitization settings
    /// </summary>
    public NameSanitizationSettings NameSanitization { get; set; } = new NameSanitizationSettings();

    /// <summary>
    /// Multiple recipients handling strategy
    /// </summary>
    public MultipleRecipientsStrategy MultipleRecipientsStrategy { get; set; } = MultipleRecipientsStrategy.PrimaryOnly;
}

public class NameSanitizationSettings
{
    public int MaxLength { get; set; } = 50;
    public string ReplaceSpacesWith { get; set; } = "_";
    public bool RemoveSpecialCharacters { get; set; } = true;
    public bool UseEmailAsFallback { get; set; } = true;
    public bool PreserveUnicode { get; set; } = false;
}

public enum DirectoryStructureType
{
    Basic,      // Current: {Year}\{Month}\{Day}\{EmailType}
    Enhanced    // New: {Year}\{Month}\{Day}\{EmailType}\{Name}
}

public enum MultipleRecipientsStrategy
{
    PrimaryOnly,        // Use first recipient only
    Combined,           // Combine multiple recipients
    SeparateFolders     // Create separate folders
}
```

#### **1.2 Database Schema Updates**

**File**: `EmailProcessor.Domain/Entities/Email.cs`

```csharp
public class Email
{
    // Existing properties...

    /// <summary>
    /// Primary recipient name for sent emails
    /// </summary>
    public string PrimaryRecipientName { get; private set; }

    /// <summary>
    /// Primary recipient email for sent emails
    /// </summary>
    public string PrimaryRecipientEmail { get; private set; }

    /// <summary>
    /// All recipients (To, CC, BCC) for sent emails
    /// </summary>
    public List<string> AllRecipients { get; private set; } = new List<string>();

    /// <summary>
    /// Sender name for received emails
    /// </summary>
    public string SenderName { get; private set; }

    // Update constructor and methods...
}
```

**File**: `EmailProcessor.Domain/Entities/Attachment.cs`

```csharp
public class Attachment
{
    // Existing properties...

    /// <summary>
    /// Full directory path where attachment is stored
    /// </summary>
    public string DirectoryPath { get; private set; }

    // Update constructor and methods...
}
```

### **Phase 2: Core Logic Implementation**

#### **2.1 Name Sanitization Service**

**New File**:
`EmailProcessor.Infrastructure/Services/NameSanitizationService.cs`

```csharp
public class NameSanitizationService
{
    private readonly NameSanitizationSettings _settings;

    public NameSanitizationService(NameSanitizationSettings settings)
    {
        _settings = settings;
    }

    public string SanitizeName(string name, string email = null)
    {
        if (string.IsNullOrWhiteSpace(name))
        {
            return UseEmailFallback(email);
        }

        var sanitized = name;

        // Remove invalid file system characters
        if (_settings.RemoveSpecialCharacters)
        {
            sanitized = RemoveInvalidCharacters(sanitized);
        }

        // Replace spaces
        sanitized = sanitized.Replace(" ", _settings.ReplaceSpacesWith);

        // Handle Unicode
        if (!_settings.PreserveUnicode)
        {
            sanitized = ConvertToAscii(sanitized);
        }

        // Truncate if too long
        if (sanitized.Length > _settings.MaxLength)
        {
            sanitized = sanitized.Substring(0, _settings.MaxLength);
        }

        // Ensure uniqueness
        sanitized = EnsureUniqueName(sanitized, email);

        return sanitized;
    }

    private string RemoveInvalidCharacters(string input)
    {
        var invalidChars = Path.GetInvalidFileNameChars();
        return string.Join("", input.Where(c => !invalidChars.Contains(c)));
    }

    private string UseEmailFallback(string email)
    {
        if (string.IsNullOrWhiteSpace(email) || !_settings.UseEmailAsFallback)
        {
            return "Unknown_User";
        }

        var domain = email.Split('@').LastOrDefault() ?? "unknown";
        return $"Unknown_User@{domain}";
    }

    // Additional helper methods...
}
```

#### **2.2 Enhanced Directory Structure Service**

**New File**:
`EmailProcessor.Infrastructure/Services/DirectoryStructureService.cs`

```csharp
public class DirectoryStructureService
{
    private readonly NameSanitizationService _nameSanitizationService;
    private readonly StorageConfiguration _storageConfig;

    public DirectoryStructureService(
        NameSanitizationService nameSanitizationService,
        StorageConfiguration storageConfig)
    {
        _nameSanitizationService = nameSanitizationService;
        _storageConfig = storageConfig;
    }

    public string CreateDirectoryStructure(EmailType emailType, DateTime timestamp,
        string senderName, string recipientName, List<string> allRecipients = null)
    {
        var baseStructure = $"{timestamp.Year}\\{timestamp.Month:D2}\\{timestamp.Day:D2}\\{emailType}";

        if (_storageConfig.DirectoryStructureType == DirectoryStructureType.Basic)
        {
            return baseStructure;
        }

        if (emailType == EmailType.Sent)
        {
            return CreateSentEmailStructure(baseStructure, recipientName, allRecipients);
        }
        else // Received
        {
            return CreateReceivedEmailStructure(baseStructure, senderName);
        }
    }

    private string CreateSentEmailStructure(string baseStructure, string recipientName, List<string> allRecipients)
    {
        var primaryRecipient = GetPrimaryRecipient(recipientName, allRecipients);
        var sanitizedName = _nameSanitizationService.SanitizeName(primaryRecipient);

        switch (_storageConfig.MultipleRecipientsStrategy)
        {
            case MultipleRecipientsStrategy.PrimaryOnly:
                return $"{baseStructure}\\{sanitizedName}";

            case MultipleRecipientsStrategy.Combined:
                return CreateCombinedRecipientsStructure(baseStructure, allRecipients);

            case MultipleRecipientsStrategy.SeparateFolders:
                return CreateSeparateFoldersStructure(baseStructure, allRecipients);

            default:
                return $"{baseStructure}\\{sanitizedName}";
        }
    }

    private string CreateReceivedEmailStructure(string baseStructure, string senderName)
    {
        var sanitizedName = _nameSanitizationService.SanitizeName(senderName);
        return $"{baseStructure}\\{sanitizedName}";
    }

    private string GetPrimaryRecipient(string recipientName, List<string> allRecipients)
    {
        if (!string.IsNullOrWhiteSpace(recipientName))
            return recipientName;

        return allRecipients?.FirstOrDefault() ?? "No_Recipients";
    }

    // Additional helper methods for multiple recipients...
}
```

#### **2.3 Update Email Processing Service**

**File**: `EmailProcessor.Service/Services/EmailProcessingService.cs`

```csharp
public class EmailProcessingService
{
    private readonly DirectoryStructureService _directoryStructureService;
    // ... existing dependencies

    public EmailProcessingService(
        // ... existing parameters
        DirectoryStructureService directoryStructureService)
    {
        _directoryStructureService = directoryStructureService;
        // ... existing assignments
    }

    private async Task<Email?> CreateEmailEntityAsync(EmailData emailData, Guid correlationId)
    {
        try
        {
            // Parse email type
            var emailType = emailData.EmailType.ToLower() switch
            {
                "sent" => EmailType.Sent,
                "received" => EmailType.Received,
                _ => EmailType.Received
            };

            // Extract recipient information for sent emails
            string primaryRecipientName = null;
            string primaryRecipientEmail = null;
            List<string> allRecipients = new List<string>();

            if (emailType == EmailType.Sent)
            {
                primaryRecipientName = emailData.RecipientTo.FirstOrDefault();
                primaryRecipientEmail = emailData.RecipientTo.FirstOrDefault();
                allRecipients.AddRange(emailData.RecipientTo);
                allRecipients.AddRange(emailData.RecipientCC);
                allRecipients.AddRange(emailData.RecipientBCC);
            }

            // Create email addresses
            var senderEmail = EmailAddress.Create(emailData.SenderEmail);
            var recipientTo = emailData.RecipientTo.Select(EmailAddress.Create).ToList();
            var recipientCC = emailData.RecipientCC.Select(EmailAddress.Create).ToList();

            // Create email entity with new fields
            var email = new Email(
                emailData.Subject,
                senderEmail,
                recipientTo,
                recipientCC,
                emailType,
                emailData.Timestamp,
                emailData.OutlookMessageId,
                primaryRecipientName,
                primaryRecipientEmail,
                allRecipients,
                emailData.SenderName);

            return email;
        }
        catch (Exception ex)
        {
            await _loggingProvider.LogErrorAsync($"Failed to create email entity", "EmailProcessingService", ex, correlationId);
            return null;
        }
    }

    private async Task<DualStorageResult> ProcessAttachmentStorageAsync(
        Attachment attachment,
        AttachmentData attachmentData,
        Guid correlationId)
    {
        try
        {
            // Convert base64 data to bytes
            var fileBytes = Convert.FromBase64String(attachmentData.FileData);

            // Create enhanced directory structure
            var directoryStructure = _directoryStructureService.CreateDirectoryStructure(
                attachment.Email.EmailType,
                attachment.Email.Timestamp,
                attachment.Email.SenderName,
                attachment.Email.PrimaryRecipientName,
                attachment.Email.AllRecipients);

            // Process with dual storage service
            var result = await _dualStorageService.SaveFileAsync(
                attachmentData.FileName,
                fileBytes,
                directoryStructure);

            // Update attachment with directory path
            attachment.UpdateDirectoryPath(directoryStructure);

            return result;
        }
        catch (Exception ex)
        {
            await _loggingProvider.LogErrorAsync($"Failed to process attachment storage for: {attachmentData.FileName}", "EmailProcessingService", ex, correlationId);

            return new DualStorageResult
            {
                FileName = attachmentData.FileName,
                IsSuccessful = false,
                ErrorMessage = ex.Message,
                LocalStorageResult = new StorageResult { StorageType = StorageType.Local, IsSuccessful = false, ErrorMessage = ex.Message },
                UncStorageResult = new StorageResult { StorageType = StorageType.Unc, IsSuccessful = false, ErrorMessage = ex.Message }
            };
        }
    }
}
```

### **Phase 3: VSTO Add-in Updates**

#### **3.1 Update Shared Models**

**File**: `EmailProcessor.Shared/Models/EmailProcessingModels.cs`

```csharp
public class EmailData
{
    // Existing properties...

    [JsonPropertyName("senderName")]
    public string SenderName { get; set; } = string.Empty;

    [JsonPropertyName("primaryRecipientName")]
    public string PrimaryRecipientName { get; set; } = string.Empty;

    [JsonPropertyName("primaryRecipientEmail")]
    public string PrimaryRecipientEmail { get; set; } = string.Empty;

    [JsonPropertyName("allRecipients")]
    public List<string> AllRecipients { get; set; } = new List<string>();
}
```

#### **3.2 Update Outlook Integration Service**

**File**: `EmailProcessor.AddIn/Services/OutlookIntegrationService.cs`

```csharp
public EmailProcessor.Shared.Models.EmailData ExtractEmailData(dynamic mailItem)
{
    lock (_extractionLock)
    {
        var emailData = new EmailProcessor.Shared.Models.EmailData
        {
            // Existing properties...
            SenderName = GetPropertyValue(mailItem, "SenderName") ?? "",
            PrimaryRecipientName = "",
            PrimaryRecipientEmail = "",
            AllRecipients = new List<string>()
        };

        // Extract recipient information
        var emailType = GetEmailType(mailItem);
        if (emailType == "Sent")
        {
            // For sent emails, extract recipient information
            var toRecipients = ExtractRecipients(mailItem, "To");
            var ccRecipients = ExtractRecipients(mailItem, "CC");
            var bccRecipients = ExtractRecipients(mailItem, "BCC");

            emailData.PrimaryRecipientName = toRecipients.FirstOrDefault() ?? "";
            emailData.PrimaryRecipientEmail = toRecipients.FirstOrDefault() ?? "";

            emailData.AllRecipients.AddRange(toRecipients);
            emailData.AllRecipients.AddRange(ccRecipients);
            emailData.AllRecipients.AddRange(bccRecipients);
        }
        else
        {
            // For received emails, sender name is already extracted
            emailData.SenderName = GetPropertyValue(mailItem, "SenderName") ?? "";
        }

        return emailData;
    }
}
```

### **Phase 4: Configuration and Migration**

#### **4.1 Configuration File Updates**

**File**: `EmailProcessor.Service/appsettings.json`

```json
{
	"EmailProcessor": {
		"Storage": {
			"LocalBasePath": "C:\\EmailAttachments",
			"UncBasePath": "\\\\server\\share\\EmailAttachments",
			"DirectoryStructureType": "Enhanced",
			"NameSanitization": {
				"MaxLength": 50,
				"ReplaceSpacesWith": "_",
				"RemoveSpecialCharacters": true,
				"UseEmailAsFallback": true,
				"PreserveUnicode": false
			},
			"MultipleRecipientsStrategy": "PrimaryOnly"
		}
	}
}
```

#### **4.2 Database Migration**

**New File**:
`EmailProcessor.Infrastructure/Database/Migrations/AddEnhancedDirectoryStructure.cs`

```csharp
public partial class AddEnhancedDirectoryStructure : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        // Add new columns to Email table
        migrationBuilder.AddColumn<string>(
            name: "PrimaryRecipientName",
            table: "Emails",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "PrimaryRecipientEmail",
            table: "Emails",
            type: "nvarchar(255)",
            maxLength: 255,
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "AllRecipients",
            table: "Emails",
            type: "nvarchar(max)",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "SenderName",
            table: "Emails",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true);

        // Add new column to Attachment table
        migrationBuilder.AddColumn<string>(
            name: "DirectoryPath",
            table: "Attachments",
            type: "nvarchar(500)",
            maxLength: 500,
            nullable: true);

        // Create indexes for performance
        migrationBuilder.CreateIndex(
            name: "IX_Emails_PrimaryRecipientName",
            table: "Emails",
            column: "PrimaryRecipientName");

        migrationBuilder.CreateIndex(
            name: "IX_Emails_SenderName",
            table: "Emails",
            column: "SenderName");
    }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
        // Remove indexes
        migrationBuilder.DropIndex(
            name: "IX_Emails_PrimaryRecipientName",
            table: "Emails");

        migrationBuilder.DropIndex(
            name: "IX_Emails_SenderName",
            table: "Emails");

        // Remove columns
        migrationBuilder.DropColumn(
            name: "PrimaryRecipientName",
            table: "Emails");

        migrationBuilder.DropColumn(
            name: "PrimaryRecipientEmail",
            table: "Emails");

        migrationBuilder.DropColumn(
            name: "AllRecipients",
            table: "Emails");

        migrationBuilder.DropColumn(
            name: "SenderName",
            table: "Emails");

        migrationBuilder.DropColumn(
            name: "DirectoryPath",
            table: "Attachments");
    }
}
```

## 🧪 Testing Strategy

### **Unit Tests**

```csharp
[TestClass]
public class NameSanitizationServiceTests
{
    [TestMethod]
    public void SanitizeName_WithSpecialCharacters_RemovesInvalidChars()
    {
        var service = new NameSanitizationService(new NameSanitizationSettings());
        var result = service.SanitizeName("John<Smith>");
        Assert.AreEqual("JohnSmith", result);
    }

    [TestMethod]
    public void SanitizeName_WithSpaces_ReplacesWithUnderscores()
    {
        var service = new NameSanitizationService(new NameSanitizationSettings());
        var result = service.SanitizeName("John Smith");
        Assert.AreEqual("John_Smith", result);
    }
}
```

### **Integration Tests**

```csharp
[TestClass]
public class EnhancedDirectoryStructureIntegrationTests
{
    [TestMethod]
    public async Task ProcessEmail_WithEnhancedStructure_CreatesCorrectDirectories()
    {
        // Test that new directory structure is created correctly
        // Verify files are saved in the right locations
        // Validate database records are updated
    }
}
```

## 📋 Implementation Checklist

### **Phase 1: Foundation**

- [ ] Update configuration classes
- [ ] Create database migration
- [ ] Update domain entities
- [ ] Update repositories

### **Phase 2: Core Services**

- [ ] Implement NameSanitizationService
- [ ] Implement DirectoryStructureService
- [ ] Update EmailProcessingService
- [ ] Add unit tests

### **Phase 3: VSTO Integration**

- [ ] Update shared models
- [ ] Update OutlookIntegrationService
- [ ] Update ThisAddIn
- [ ] Test VSTO add-in

### **Phase 4: Migration**

- [ ] Create migration scripts
- [ ] Test migration on sample data
- [ ] Deploy with feature flag
- [ ] Monitor and validate

### **Phase 5: Validation**

- [ ] Performance testing
- [ ] Edge case testing
- [ ] User acceptance testing
- [ ] Documentation updates

## 🎯 Expected Benefits

1. **Improved Organization**: Easy to find attachments by sender/recipient
2. **Better Searchability**: Logical folder structure for file system search
3. **Enhanced User Experience**: Intuitive organization matching user
   expectations
4. **Scalability**: Better performance with large numbers of attachments
5. **Maintainability**: Clear structure for backup and maintenance

## 🔍 Edge Cases and Considerations

### **Name Sanitization**

- **Special Characters**: Remove invalid file system characters
- **Long Names**: Truncate to configurable maximum length
- **Missing Names**: Use email address as fallback
- **Unicode Characters**: Convert to ASCII or preserve Unicode
- **Duplicate Names**: Add email domain or numbering

### **Multiple Recipients**

- **Primary Recipient**: Use first recipient in To list
- **Fallback Strategy**: Use CC if no To recipients, BCC if no CC
- **Combined Names**: Create combined folder names for multiple recipients
- **Separate Folders**: Create hierarchical folder structure

### **Performance Considerations**

- **Directory Creation**: Efficient directory creation with proper error
  handling
- **File Operations**: Atomic file operations to prevent corruption
- **Database Indexes**: Proper indexing for new fields
- **Memory Usage**: Efficient handling of large recipient lists

This enhanced directory structure will significantly improve the organization
and accessibility of email attachments while maintaining system stability and
performance.
