using System;
using System.IO;
using System.Threading.Tasks;
using EmailProcessor.Domain.Interfaces;
using EmailProcessor.Domain.ValueObjects;
using EmailProcessor.Infrastructure.Storage.LocalStorage;
using FluentAssertions;
using Moq;
using Xunit;

namespace EmailProcessor.Infrastructure.Tests.Storage
{
    public class LocalStorageProviderTests : IDisposable
    {
        private readonly string _testBasePath;
        private readonly Mock<ILoggingProvider> _mockLoggingProvider;
        private readonly LocalStorageProvider _localStorageProvider;

        public LocalStorageProviderTests()
        {
            _testBasePath = Path.Combine(Path.GetTempPath(), "EmailProcessor_Tests", Guid.NewGuid().ToString());
            _mockLoggingProvider = new Mock<ILoggingProvider>();

            // Create test directory
            Directory.CreateDirectory(_testBasePath);

            _localStorageProvider = new LocalStorageProvider(_testBasePath, _mockLoggingProvider.Object);
        }

        public void Dispose()
        {
            // Clean up test directory
            if (Directory.Exists(_testBasePath))
            {
                try
                {
                    Directory.Delete(_testBasePath, true);
                }
                catch
                {
                    // Ignore cleanup errors in tests
                }
            }
        }

        [Fact]
        public void Constructor_WithValidBasePath_CreatesInstance()
        {
            // Act & Assert
            _localStorageProvider.Should().NotBeNull();
            _localStorageProvider.StorageType.Should().Be(StorageType.Local);
            _localStorageProvider.BasePath.Should().Be(_testBasePath);
        }

        [Fact]
        public void Constructor_WithNullBasePath_ThrowsArgumentException()
        {
            // Act & Assert  
            var exception = Assert.Throws<ArgumentException>(() =>
                new LocalStorageProvider(null, _mockLoggingProvider.Object));

            exception.ParamName.Should().Be("basePath");
            exception.Message.Should().Contain("BasePath cannot be null or empty");
        }

        [Fact]
        public void Constructor_WithEmptyBasePath_ThrowsArgumentException()
        {
            // Act & Assert
            var exception = Assert.Throws<ArgumentException>(() =>
                new LocalStorageProvider("", _mockLoggingProvider.Object));

            exception.ParamName.Should().Be("basePath");
            exception.Message.Should().Contain("BasePath cannot be null or empty");
        }

        [Fact]
        public void Constructor_WithNonExistentBasePath_CreatesDirectory()
        {
            // Arrange
            var nonExistentPath = Path.Combine(Path.GetTempPath(), "EmailProcessor_NonExistent", Guid.NewGuid().ToString());

            try
            {
                // Act
                var provider = new LocalStorageProvider(nonExistentPath, _mockLoggingProvider.Object);

                // Assert
                Directory.Exists(nonExistentPath).Should().BeTrue();
                provider.BasePath.Should().Be(Path.GetFullPath(nonExistentPath));
            }
            finally
            {
                // Cleanup
                if (Directory.Exists(nonExistentPath))
                {
                    Directory.Delete(nonExistentPath, true);
                }
            }
        }

        [Fact]
        public async Task SaveFileAsync_WithByteArray_ValidInput_SavesFileSuccessfully()
        {
            // Arrange
            var fileName = "test.txt";
            var fileData = System.Text.Encoding.UTF8.GetBytes("Hello, World!");
            var directoryPath = "testdir";

            // Act
            var result = await _localStorageProvider.SaveFileAsync(fileData, fileName, directoryPath);

            // Assert
            result.Should().NotBeNull();
            result.Value.Should().NotBeNullOrEmpty();

            var expectedPath = Path.Combine(_testBasePath, directoryPath, fileName);
            File.Exists(expectedPath).Should().BeTrue();

            var savedContent = await File.ReadAllBytesAsync(expectedPath);
            savedContent.Should().BeEquivalentTo(fileData);
        }

        [Fact]
        public async Task SaveFileAsync_WithByteArray_NullFileData_ThrowsArgumentNullException()
        {
            // Arrange
            var fileName = "test.txt";
            byte[] fileData = null;

            // Act & Assert
            var exception = await Assert.ThrowsAsync<ArgumentNullException>(() =>
                _localStorageProvider.SaveFileAsync(fileData, fileName, null));

            exception.ParamName.Should().Be("fileData");
        }

        [Fact]
        public async Task SaveFileAsync_WithByteArray_NullFileName_ThrowsArgumentException()
        {
            // Arrange
            var fileData = new byte[] { 1, 2, 3 };
            string fileName = null;

            // Act & Assert
            var exception = await Assert.ThrowsAsync<ArgumentException>(() =>
                _localStorageProvider.SaveFileAsync(fileData, fileName, null));

            exception.ParamName.Should().Be("fileName");
        }

        [Fact]
        public async Task SaveFileAsync_WithByteArray_EmptyFileName_ThrowsArgumentException()
        {
            // Arrange
            var fileData = new byte[] { 1, 2, 3 };
            var fileName = "";

            // Act & Assert
            var exception = await Assert.ThrowsAsync<ArgumentException>(() =>
                _localStorageProvider.SaveFileAsync(fileData, fileName, null));

            exception.ParamName.Should().Be("fileName");
        }

        [Fact]
        public async Task SaveFileAsync_WithStream_ValidInput_SavesFileSuccessfully()
        {
            // Arrange
            var fileName = "test_stream.txt";
            var testContent = "Hello from stream!";
            var directoryPath = "streamdir";

            using var stream = new MemoryStream(System.Text.Encoding.UTF8.GetBytes(testContent));

            // Act
            var result = await _localStorageProvider.SaveFileAsync(stream, fileName, directoryPath);

            // Assert
            result.Should().NotBeNull();
            result.Value.Should().NotBeNullOrEmpty();

            var expectedPath = Path.Combine(_testBasePath, directoryPath, fileName);
            File.Exists(expectedPath).Should().BeTrue();

            var savedContent = await File.ReadAllTextAsync(expectedPath);
            savedContent.Should().Be(testContent);
        }

        [Fact]
        public async Task SaveFileAsync_WithStream_NullStream_ThrowsArgumentNullException()
        {
            // Arrange
            var fileName = "test.txt";
            Stream stream = null;

            // Act & Assert
            var exception = await Assert.ThrowsAsync<ArgumentNullException>(() =>
                _localStorageProvider.SaveFileAsync(stream, fileName, null));

            exception.ParamName.Should().Be("fileStream");
        }

        [Fact]
        public async Task SaveFileAsync_CreatesDirectoryStructure()
        {
            // Arrange
            var fileName = "test.txt";
            var fileData = new byte[] { 1, 2, 3 };
            var nestedPath = Path.Combine("level1", "level2", "level3");

            // Act
            await _localStorageProvider.SaveFileAsync(fileData, fileName, nestedPath);

            // Assert
            var expectedDirPath = Path.Combine(_testBasePath, nestedPath);
            var expectedFilePath = Path.Combine(expectedDirPath, fileName);

            Directory.Exists(expectedDirPath).Should().BeTrue();
            File.Exists(expectedFilePath).Should().BeTrue();
        }

        [Fact]
        public async Task SaveFileAsync_OverwritesExistingFile()
        {
            // Arrange
            var fileName = "overwrite_test.txt";
            var initialData = System.Text.Encoding.UTF8.GetBytes("Initial content");
            var newData = System.Text.Encoding.UTF8.GetBytes("New content");

            // Act - Save initial file
            await _localStorageProvider.SaveFileAsync(initialData, fileName, null);

            // Act - Overwrite with new data
            var result = await _localStorageProvider.SaveFileAsync(newData, fileName, null);

            // Assert
            result.Should().NotBeNull();

            var filePath = Path.Combine(_testBasePath, fileName);
            var savedContent = await File.ReadAllBytesAsync(filePath);
            savedContent.Should().BeEquivalentTo(newData);
        }

        [Fact]
        public async Task CreateDirectoryAsync_CreatesDirectory()
        {
            // Arrange
            var directoryPath = "test_create_dir";

            // Act
            await _localStorageProvider.CreateDirectoryAsync(directoryPath);

            // Assert
            var fullPath = Path.Combine(_testBasePath, directoryPath);
            Directory.Exists(fullPath).Should().BeTrue();
        }

        [Fact]
        public async Task FileExistsAsync_ExistingFile_ReturnsTrue()
        {
            // Arrange
            var fileName = "exists_test.txt";
            var filePath = Path.Combine(_testBasePath, fileName);
            await File.WriteAllTextAsync(filePath, "test content");

            // Act
            var result = await _localStorageProvider.FileExistsAsync(filePath);

            // Assert
            result.Should().BeTrue();
        }

        [Fact]
        public async Task FileExistsAsync_NonExistentFile_ReturnsFalse()
        {
            // Arrange
            var filePath = Path.Combine(_testBasePath, "nonexistent.txt");

            // Act
            var result = await _localStorageProvider.FileExistsAsync(filePath);

            // Assert
            result.Should().BeFalse();
        }

        [Fact]
        public async Task DirectoryExistsAsync_ExistingDirectory_ReturnsTrue()
        {
            // Arrange
            var directoryPath = "existing_dir";
            var fullPath = Path.Combine(_testBasePath, directoryPath);
            Directory.CreateDirectory(fullPath);

            // Act
            var result = await _localStorageProvider.DirectoryExistsAsync(directoryPath);

            // Assert
            result.Should().BeTrue();
        }

        [Fact]
        public async Task DirectoryExistsAsync_NonExistentDirectory_ReturnsFalse()
        {
            // Arrange
            var directoryPath = "nonexistent_dir";

            // Act
            var result = await _localStorageProvider.DirectoryExistsAsync(directoryPath);

            // Assert
            result.Should().BeFalse();
        }

        [Fact]
        public async Task DeleteFileAsync_ExistingFile_DeletesFile()
        {
            // Arrange
            var fileName = "delete_test.txt";
            var filePath = Path.Combine(_testBasePath, fileName);
            await File.WriteAllTextAsync(filePath, "test content");

            File.Exists(filePath).Should().BeTrue(); // Verify file exists

            // Act
            await _localStorageProvider.DeleteFileAsync(filePath);

            // Assert
            File.Exists(filePath).Should().BeFalse();
        }

        [Fact]
        public async Task DeleteFileAsync_NonExistentFile_DoesNotThrow()
        {
            // Arrange
            var filePath = Path.Combine(_testBasePath, "nonexistent.txt");

            // Act & Assert - Should not throw
            await _localStorageProvider.DeleteFileAsync(filePath);
        }

        [Fact]
        public async Task GetFileInfoAsync_ExistingFile_ReturnsFileInfo()
        {
            // Arrange
            var fileName = "info_test.txt";
            var filePath = Path.Combine(_testBasePath, fileName);
            var testContent = "test content for file info";
            await File.WriteAllTextAsync(filePath, testContent);

            // Act
            var result = await _localStorageProvider.GetFileInfoAsync(filePath);

            // Assert
            result.Should().NotBeNull();
            result.Exists.Should().BeTrue();
            result.Name.Should().Be(fileName);
            result.Length.Should().Be(System.Text.Encoding.UTF8.GetByteCount(testContent));
        }

        [Fact]
        public async Task GetAvailableSpaceAsync_ReturnsPositiveValue()
        {
            // Act
            var result = await _localStorageProvider.GetAvailableSpaceAsync();

            // Assert
            result.Should().BeGreaterThan(0);
        }

        [Fact]
        public async Task TestConnectivityAsync_WithValidDirectory_ReturnsTrue()
        {
            // Act
            var result = await _localStorageProvider.TestConnectivityAsync();

            // Assert
            result.Should().BeTrue();
        }

        [Fact]
        public async Task SaveFileAsync_WithRetryOnTransientError_EventuallySucceeds()
        {
            // This test simulates a transient error that resolves after retry
            // In a real scenario, this would be more complex to test properly
            // For now, we test the normal success case

            // Arrange
            var fileName = "retry_test.txt";
            var fileData = new byte[] { 1, 2, 3, 4, 5 };

            // Act
            var result = await _localStorageProvider.SaveFileAsync(fileData, fileName, null);

            // Assert
            result.Should().NotBeNull();
            var filePath = Path.Combine(_testBasePath, fileName);
            File.Exists(filePath).Should().BeTrue();
        }
    }
}