using System;
using System.IO;
using System.Reflection;
using Microsoft.Win32;
using System.Security.Principal;
using System.Diagnostics;

namespace EmailProcessor.AddIn.Configuration
{
    /// <summary>
    /// VSTO deployment utility for managing add-in installation and configuration
    /// Phase 4: Comprehensive deployment and troubleshooting tools
    /// </summary>
    public static class VSTODeploymentUtility
    {
        private const string VSTO_REGISTRY_KEY = @"SOFTWARE\Microsoft\Office\Outlook\Addins\EmailProcessor.AddIn";
        private const string VSTO_LOAD_BEHAVIOR_KEY = "LoadBehavior";
        private const string VSTO_MANIFEST_KEY = "Manifest";
        private const string VSTO_FRIENDLY_NAME = "Email Attachment Processor";
        private const string VSTO_DESCRIPTION = "Automatically processes email attachments and saves them to designated storage locations";

        /// <summary>
        /// Checks if the current user has administrative privileges
        /// </summary>
        public static bool IsAdministrator()
        {
            try
            {
                using (var identity = WindowsIdentity.GetCurrent())
                {
                    var principal = new WindowsPrincipal(identity);
                    return principal.IsInRole(WindowsBuiltInRole.Administrator);
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Checks if the VSTO add-in is properly registered
        /// </summary>
        public static bool IsAddInRegistered()
        {
            try
            {
                using (var key = Registry.CurrentUser.OpenSubKey(VSTO_REGISTRY_KEY))
                {
                    if (key == null)
                        return false;

                    var loadBehavior = key.GetValue(VSTO_LOAD_BEHAVIOR_KEY);
                    var manifest = key.GetValue(VSTO_MANIFEST_KEY);

                    return loadBehavior != null && manifest != null;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Registers the VSTO add-in in the registry
        /// </summary>
        public static bool RegisterAddIn()
        {
            try
            {
                if (!IsAdministrator())
                {
                    throw new InvalidOperationException("Administrative privileges required to register VSTO add-in");
                }

                var assemblyLocation = Assembly.GetExecutingAssembly().Location;
                var manifestPath = Path.Combine(Path.GetDirectoryName(assemblyLocation), "EmailProcessor.AddIn.vsto");

                if (!File.Exists(manifestPath))
                {
                    throw new FileNotFoundException($"VSTO manifest not found at: {manifestPath}");
                }

                using (var key = Registry.CurrentUser.CreateSubKey(VSTO_REGISTRY_KEY))
                {
                    if (key != null)
                    {
                        key.SetValue("FriendlyName", VSTO_FRIENDLY_NAME);
                        key.SetValue("Description", VSTO_DESCRIPTION);
                        key.SetValue("LoadBehavior", 3); // 3 = Load on startup
                        key.SetValue("Manifest", manifestPath);
                        key.SetValue("Publisher", "Email Processor Team");
                        key.SetValue("Version", GetAssemblyVersion());
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error registering VSTO add-in: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Unregisters the VSTO add-in from the registry
        /// </summary>
        public static bool UnregisterAddIn()
        {
            try
            {
                if (!IsAdministrator())
                {
                    throw new InvalidOperationException("Administrative privileges required to unregister VSTO add-in");
                }

                Registry.CurrentUser.DeleteSubKeyTree(VSTO_REGISTRY_KEY, false);
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error unregistering VSTO add-in: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Enables the VSTO add-in
        /// </summary>
        public static bool EnableAddIn()
        {
            try
            {
                using (var key = Registry.CurrentUser.CreateSubKey(VSTO_REGISTRY_KEY))
                {
                    if (key != null)
                    {
                        key.SetValue(VSTO_LOAD_BEHAVIOR_KEY, 3); // 3 = Load on startup
                        return true;
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error enabling VSTO add-in: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Disables the VSTO add-in
        /// </summary>
        public static bool DisableAddIn()
        {
            try
            {
                using (var key = Registry.CurrentUser.CreateSubKey(VSTO_REGISTRY_KEY))
                {
                    if (key != null)
                    {
                        key.SetValue(VSTO_LOAD_BEHAVIOR_KEY, 0); // 0 = Disabled
                        return true;
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error disabling VSTO add-in: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Gets the current load behavior of the add-in
        /// </summary>
        public static int GetLoadBehavior()
        {
            try
            {
                using (var key = Registry.CurrentUser.OpenSubKey(VSTO_REGISTRY_KEY))
                {
                    if (key != null)
                    {
                        var loadBehavior = key.GetValue(VSTO_LOAD_BEHAVIOR_KEY);
                        if (loadBehavior != null && int.TryParse(loadBehavior.ToString(), out var value))
                        {
                            return value;
                        }
                    }
                }
                return 0; // Disabled
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// Checks if the VSTO runtime is installed
        /// </summary>
        public static bool IsVSTORuntimeInstalled()
        {
            try
            {
                // Check for VSTO runtime in registry
                using (var key = Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Microsoft\VSTO Runtime Setup\v4"))
                {
                    if (key != null)
                    {
                        var version = key.GetValue("Version");
                        return version != null;
                    }
                }

                // Check for VSTO runtime in Program Files
                var vstoPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFiles), 
                    "Microsoft Visual Studio Tools for Office", "PIA", "Office16");
                return Directory.Exists(vstoPath);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Gets the VSTO runtime version
        /// </summary>
        public static string GetVSTORuntimeVersion()
        {
            try
            {
                using (var key = Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Microsoft\VSTO Runtime Setup\v4"))
                {
                    if (key != null)
                    {
                        var version = key.GetValue("Version");
                        return version?.ToString() ?? "Unknown";
                    }
                }
                return "Not Found";
            }
            catch
            {
                return "Error";
            }
        }

        /// <summary>
        /// Checks if the required .NET Framework version is installed
        /// </summary>
        public static bool IsDotNetFrameworkInstalled()
        {
            try
            {
                using (var key = Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full\"))
                {
                    if (key != null)
                    {
                        var release = key.GetValue("Release");
                        if (release != null && int.TryParse(release.ToString(), out var releaseValue))
                        {
                            // .NET Framework 4.8 or later
                            return releaseValue >= 528040;
                        }
                    }
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Gets the .NET Framework version
        /// </summary>
        public static string GetDotNetFrameworkVersion()
        {
            try
            {
                using (var key = Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full\"))
                {
                    if (key != null)
                    {
                        var version = key.GetValue("Version");
                        return version?.ToString() ?? "Unknown";
                    }
                }
                return "Not Found";
            }
            catch
            {
                return "Error";
            }
        }

        /// <summary>
        /// Creates a diagnostic report for troubleshooting
        /// </summary>
        public static string CreateDiagnosticReport()
        {
            var report = new System.Text.StringBuilder();
            report.AppendLine("=== Email Processor VSTO Add-in Diagnostic Report ===");
            report.AppendLine($"Generated: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine();

            // System Information
            report.AppendLine("--- System Information ---");
            report.AppendLine($"OS Version: {Environment.OSVersion}");
            report.AppendLine($"Machine Name: {Environment.MachineName}");
            report.AppendLine($"User Name: {Environment.UserName}");
            report.AppendLine($"Is Administrator: {IsAdministrator()}");
            report.AppendLine();

            // .NET Framework Information
            report.AppendLine("--- .NET Framework Information ---");
            report.AppendLine($"Installed: {IsDotNetFrameworkInstalled()}");
            report.AppendLine($"Version: {GetDotNetFrameworkVersion()}");
            report.AppendLine();

            // VSTO Runtime Information
            report.AppendLine("--- VSTO Runtime Information ---");
            report.AppendLine($"Installed: {IsVSTORuntimeInstalled()}");
            report.AppendLine($"Version: {GetVSTORuntimeVersion()}");
            report.AppendLine();

            // Add-in Registration Information
            report.AppendLine("--- Add-in Registration Information ---");
            report.AppendLine($"Registered: {IsAddInRegistered()}");
            report.AppendLine($"Load Behavior: {GetLoadBehavior()} ({GetLoadBehaviorDescription(GetLoadBehavior())})");
            report.AppendLine();

            // Assembly Information
            report.AppendLine("--- Assembly Information ---");
            var assembly = Assembly.GetExecutingAssembly();
            report.AppendLine($"Location: {assembly.Location}");
            report.AppendLine($"Version: {assembly.GetName().Version}");
            report.AppendLine($"Full Name: {assembly.FullName}");
            report.AppendLine();

            // Registry Information
            report.AppendLine("--- Registry Information ---");
            try
            {
                using (var key = Registry.CurrentUser.OpenSubKey(VSTO_REGISTRY_KEY))
                {
                    if (key != null)
                    {
                        foreach (var valueName in key.GetValueNames())
                        {
                            var value = key.GetValue(valueName);
                            report.AppendLine($"{valueName}: {value}");
                        }
                    }
                    else
                    {
                        report.AppendLine("Registry key not found");
                    }
                }
            }
            catch (Exception ex)
            {
                report.AppendLine($"Error reading registry: {ex.Message}");
            }

            return report.ToString();
        }

        /// <summary>
        /// Saves diagnostic report to file
        /// </summary>
        public static bool SaveDiagnosticReport(string filePath)
        {
            try
            {
                var report = CreateDiagnosticReport();
                File.WriteAllText(filePath, report);
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving diagnostic report: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Opens the diagnostic report in the default text editor
        /// </summary>
        public static bool OpenDiagnosticReport(string filePath)
        {
            try
            {
                if (File.Exists(filePath))
                {
                    Process.Start("notepad.exe", filePath);
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error opening diagnostic report: {ex.Message}");
                return false;
            }
        }

        private static string GetAssemblyVersion()
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();
                return assembly.GetName().Version?.ToString() ?? "*******";
            }
            catch
            {
                return "*******";
            }
        }

        private static string GetLoadBehaviorDescription(int loadBehavior)
        {
            switch (loadBehavior)
            {
                case 0:
                    return "Disabled";
                case 1:
                    return "Load on demand";
                case 2:
                    return "Load at startup";
                case 3:
                    return "Load on startup";
                case 8:
                    return "Load on demand (UI)";
                case 16:
                    return "Load on demand (command line)";
                default:
                    return "Unknown";
            }
        }
    }
} 