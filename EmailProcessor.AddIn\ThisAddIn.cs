﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using Outlook = Microsoft.Office.Interop.Outlook;
using Office = Microsoft.Office.Core;
using EmailProcessor.AddIn.Interfaces;
using EmailProcessor.AddIn.Models;
using EmailProcessor.AddIn.Services;
using EmailProcessor.AddIn.Communication;
using EmailProcessor.AddIn.Configuration;
using EmailProcessor.Shared.Models;
using System.Threading;

namespace EmailProcessor.AddIn
{
    public partial class ThisAddIn
    {
        private IVSTOConfiguration _configuration;
        private IOutlookIntegration _outlookIntegration;
        private INamedPipeClient _namedPipeClient;
        private bool _isInitialized = false;
        private readonly object _processingLock = new object();
        private SemaphoreSlim _concurrentProcessingSemaphore;

        // Phase 4: Enhanced error handling and monitoring
        private int _consecutiveErrors = 0;
        private DateTime _lastErrorTime = DateTime.MinValue;
        private readonly object _errorTrackingLock = new object();



        private void ThisAddIn_Startup(object sender, System.EventArgs e)
        {
            try
            {
                InitializeAddIn();
            }
            catch (Exception ex)
            {
                LogError("Add-in startup failed", ex);
                MessageBox.Show($"Email Processor Add-in failed to start: {ex.Message}", "Email Processor Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ThisAddIn_Shutdown(object sender, System.EventArgs e)
        {
            try
            {
                CleanupAddIn();
            }
            catch (Exception ex)
            {
                LogError("Add-in shutdown error", ex);
            }
        }

        private void InitializeAddIn()
        {
            try
            {
                // Initialize configuration
                _configuration = new VSTOConfiguration();

                // Phase 4: Initialize concurrent processing semaphore with default value first, then update from configuration
                _concurrentProcessingSemaphore = new SemaphoreSlim(5, 5); // Default to 5 concurrent operations
                _concurrentProcessingSemaphore = new SemaphoreSlim(_configuration.MaxConcurrentProcessing, _configuration.MaxConcurrentProcessing);

                // Initialize services
                _outlookIntegration = new OutlookIntegrationService(_configuration);
                _namedPipeClient = new NamedPipeClient(_configuration.NamedPipeName);

                // Phase 4: Set up enhanced event handlers
                SetupEventHandlers();

                // Set up Outlook event handlers
                SetupOutlookEventHandlers();

                // Connect to Windows Service
                ConnectToWindowsService();

                _isInitialized = true;

                LogInformation("Email Processor Add-in initialized successfully");
            }
            catch (Exception ex)
            {
                LogError("Error initializing add-in", ex);
                throw;
            }
        }

        private void SetupEventHandlers()
        {
            try
            {
                // Phase 4: Enhanced connection event handling
                _namedPipeClient.Connected += OnConnected;
                _namedPipeClient.ConnectionLost += OnConnectionLost;
                _namedPipeClient.ConnectionError += OnConnectionError;

                LogInformation("Event handlers set up successfully");
            }
            catch (Exception ex)
            {
                LogError("Error setting up event handlers", ex);
                throw;
            }
        }

        private void SetupOutlookEventHandlers()
        {
            try
            {
                // Hook up to Application events
                Application.NewMailEx += Application_NewMailEx;
                Application.ItemSend += Application_ItemSend;

                LogInformation("Outlook event handlers set up successfully");
            }
            catch (Exception ex)
            {
                LogError("Error setting up Outlook event handlers", ex);
                throw;
            }
        }

        private void ConnectToWindowsService()
        {
            try
            {
                var connected = Task.Run(async () => await _namedPipeClient.ConnectAsync(_configuration.ConnectionTimeoutSeconds)).Result;
                if (connected)
                {
                    LogInformation("Successfully connected to Windows Service");
                }
                else
                {
                    LogWarning("Failed to connect to Windows Service - will retry on first email");
                }
            }
            catch (Exception ex)
            {
                LogError("Error connecting to Windows Service", ex);
            }
        }

        private void Application_NewMailEx(string EntryIDCollection)
        {
            try
            {
                if (!_isInitialized || !_configuration.ProcessReceivedEmails)
                    return;

                // Phase 4: Check error threshold
                if (IsErrorThresholdExceeded())
                {
                    LogWarning("Skipping email processing due to error threshold");
                    return;
                }

                // Process received email
                ProcessEmail(EntryIDCollection, "Received");
            }
            catch (Exception ex)
            {
                LogError("Error processing received email", ex);
                // Don't show error to user for received emails to avoid disruption
            }
        }

        private void Application_ItemSend(object Item, ref bool Cancel)
        {
            try
            {
                if (!_isInitialized || !_configuration.ProcessSentEmails)
                    return;

                // Phase 4: Check error threshold
                if (IsErrorThresholdExceeded())
                {
                    LogWarning("Skipping email processing due to error threshold");
                    return;
                }

                // Process sent email
                ProcessEmail(Item, "Sent");
            }
            catch (Exception ex)
            {
                LogError("Error processing sent email", ex);
                // Don't show error to user for sent emails to avoid disruption
            }
        }

        private async void ProcessEmail(object emailItem, string emailType)
        {
            // Phase 4: Use semaphore to limit concurrent processing
            if (!await _concurrentProcessingSemaphore.WaitAsync(TimeSpan.FromSeconds(5)))
            {
                LogWarning("Timeout waiting for processing slot - skipping email");
                return;
            }

            try
            {
                // Use lock to prevent concurrent processing of the same email
                lock (_processingLock)
                {
                    try
                    {
                        // Extract email data
                        var emailData = _outlookIntegration.ExtractEmailData(emailItem);

                        // Phase 4: Enhanced email filtering
                        if (!ShouldProcessEmail(emailData, emailItem))
                        {
                            LogInformation($"Email skipped: {emailData.Subject}");
                            return;
                        }

                        // Extract attachment data
                        var attachments = _outlookIntegration.ExtractAttachmentData(emailItem, _configuration.MaxFileSizeBytes);

                        if (!attachments.Any())
                        {
                            LogInformation($"No processable attachments found in email: {emailData.Subject}");
                            return;
                        }

                        // Phase 4: Check attachment count limit
                        if (attachments.Count > _configuration.MaxAttachmentCount)
                        {
                            LogWarning($"Email has too many attachments ({attachments.Count}) - processing first {_configuration.MaxAttachmentCount}");
                            attachments = attachments.Take(_configuration.MaxAttachmentCount).ToList();
                        }

                        // Create processing message
                        var processingMessage = new ProcessingMessage
                        {
                            Data = new EmailProcessingData
                            {
                                Email = emailData,
                                Attachments = attachments
                            }
                        };

                        // Send to Windows Service asynchronously
                        if (_configuration.EnableAsyncProcessing)
                        {
                            Task.Run(async () => await SendToWindowsServiceAsync(processingMessage));
                        }
                        else
                        {
                            // Synchronous processing for debugging
                            Task.Run(async () => await SendToWindowsServiceAsync(processingMessage)).Wait();
                        }

                        LogInformation($"Email queued for processing: {emailData.Subject} with {attachments.Count} attachments");
                    }
                    catch (Exception ex)
                    {
                        LogError($"Error processing email: {ex.Message}", ex);
                        TrackError();
                    }
                }
            }
            finally
            {
                _concurrentProcessingSemaphore.Release();
            }
        }

        private bool ShouldProcessEmail(EmailProcessor.Shared.Models.EmailData emailData, object emailItem)
        {
            try
            {
                // Check if email should be processed based on configuration
                if (!_outlookIntegration.ShouldProcessEmail(emailItem))
                    return false;

                // Phase 4: Additional filtering based on enhanced configuration
                if (_configuration.IsSenderExcluded(emailData.SenderEmail))
                {
                    LogInformation($"Email from excluded sender: {emailData.SenderEmail}");
                    return false;
                }

                if (_configuration.IsEmailTooOld(emailData.Timestamp))
                {
                    LogInformation($"Email too old: {emailData.Timestamp}");
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                LogError("Error checking if email should be processed", ex);
                return false;
            }
        }

        private async Task SendToWindowsServiceAsync(ProcessingMessage message)
        {
            try
            {
                // Phase 4: Enhanced connection management with retry logic
                if (!_namedPipeClient.IsConnected)
                {
                    var connected = await ConnectWithRetryAsync();
                    if (!connected)
                    {
                        LogError("Failed to connect to Windows Service after retries");
                        return;
                    }
                }

                // Send message with retry logic
                ProcessingResponse response = null;
                var retryCount = 0;
                var maxRetries = _configuration.RetryCount;

                while (retryCount <= maxRetries)
                {
                    try
                    {
                        response = await _namedPipeClient.SendMessageAsync(message);
                        break; // Success, exit retry loop
                    }
                    catch (Exception ex)
                    {
                        retryCount++;
                        LogWarning($"Send attempt {retryCount} failed: {ex.Message}");

                        if (retryCount <= maxRetries)
                        {
                            var delay = Math.Min(_configuration.RetryDelaySeconds * retryCount, _configuration.MaxRetryDelaySeconds);
                            await Task.Delay(TimeSpan.FromSeconds(delay));
                        }
                        else
                        {
                            LogError("Failed to send message after all retries", ex);
                            TrackError();
                            return;
                        }
                    }
                }

                if (response != null)
                {
                    if (response.Success)
                    {
                        LogInformation($"Email processed successfully: {message.Data.Email.Subject}");
                        ResetErrorTracking();
                    }
                    else
                    {
                        LogError($"Email processing failed: {response.Message}");
                        TrackError();
                    }
                }
            }
            catch (Exception ex)
            {
                LogError("Error sending message to Windows Service", ex);
                TrackError();
            }
        }

        private async Task<bool> ConnectWithRetryAsync()
        {
            var retryCount = 0;
            var maxRetries = _configuration.RetryCount;

            while (retryCount <= maxRetries)
            {
                try
                {
                    var connected = await _namedPipeClient.ConnectAsync(_configuration.ConnectionTimeoutSeconds);
                    if (connected)
                    {
                        LogInformation("Successfully connected to Windows Service");
                        return true;
                    }
                }
                catch (Exception ex)
                {
                    LogWarning($"Connection attempt {retryCount + 1} failed: {ex.Message}");
                }

                retryCount++;
                if (retryCount <= maxRetries)
                {
                    var delay = Math.Min(_configuration.RetryDelaySeconds * retryCount, _configuration.MaxRetryDelaySeconds);
                    await Task.Delay(TimeSpan.FromSeconds(delay));
                }
            }

            return false;
        }

        private void OnConnectionLost(object sender, EventArgs e)
        {
            LogWarning("Connection to Windows Service lost");
            TrackError();
        }

        private void OnConnected(object sender, EventArgs e)
        {
            LogInformation("Connected to Windows Service");
            ResetErrorTracking();
        }

        private void OnConnectionError(object sender, ConnectionErrorEventArgs e)
        {
            LogError($"Connection error: {e.ErrorMessage}");
            TrackError();
        }

        private void CleanupAddIn()
        {
            try
            {
                if (_namedPipeClient != null)
                {
                    _namedPipeClient.Connected -= OnConnected;
                    _namedPipeClient.ConnectionLost -= OnConnectionLost;
                    _namedPipeClient.ConnectionError -= OnConnectionError;
                    _namedPipeClient.Dispose();
                }

                _concurrentProcessingSemaphore?.Dispose();

                LogInformation("Add-in cleanup completed");
            }
            catch (Exception ex)
            {
                LogError("Error during add-in cleanup", ex);
            }
        }

        // Phase 4: Enhanced error tracking and logging
        private void TrackError()
        {
            lock (_errorTrackingLock)
            {
                _consecutiveErrors++;
                _lastErrorTime = DateTime.Now;
            }
        }

        private void ResetErrorTracking()
        {
            lock (_errorTrackingLock)
            {
                _consecutiveErrors = 0;
                _lastErrorTime = DateTime.MinValue;
            }
        }

        private bool IsErrorThresholdExceeded()
        {
            lock (_errorTrackingLock)
            {
                // If we've had more than 5 consecutive errors in the last 5 minutes, stop processing
                if (_consecutiveErrors > 5 && _lastErrorTime > DateTime.Now.AddMinutes(-5))
                {
                    return true;
                }

                // Reset error count if it's been more than 5 minutes since last error
                if (_lastErrorTime < DateTime.Now.AddMinutes(-5))
                {
                    _consecutiveErrors = 0;
                }

                return false;
            }
        }

        private void LogInformation(string message)
        {
            if (_configuration.EnableDebugLogging)
            {
                System.Diagnostics.Debug.WriteLine($"[INFO] {DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}");
            }
        }

        private void LogWarning(string message)
        {
            System.Diagnostics.Debug.WriteLine($"[WARN] {DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}");
        }

        private void LogError(string message, Exception ex = null)
        {
            var errorMessage = $"[ERROR] {DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}";
            if (ex != null)
            {
                errorMessage += $" - Exception: {ex.Message}";
            }
            System.Diagnostics.Debug.WriteLine(errorMessage);
        }

        private void InternalStartup()
        {
            // This method is required for VSTO add-in startup
        }
    }
}

