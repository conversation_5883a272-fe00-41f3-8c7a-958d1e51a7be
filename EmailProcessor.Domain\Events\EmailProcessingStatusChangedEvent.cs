using System;
using EmailProcessor.Domain.Entities;
using EmailProcessor.Domain.ValueObjects;

namespace EmailProcessor.Domain.Events
{
    /// <summary>
    /// Domain event raised when an email's processing status changes
    /// </summary>
    public class EmailProcessingStatusChangedEvent : IDomainEvent
    {
        public Email Email { get; }
        public ProcessingStatus NewStatus { get; }
        public DateTime OccurredOn { get; }

        public EmailProcessingStatusChangedEvent(Email email, ProcessingStatus newStatus)
        {
            Email = email ?? throw new ArgumentNullException(nameof(email));
            NewStatus = newStatus;
            OccurredOn = DateTime.UtcNow;
        }
    }
} 