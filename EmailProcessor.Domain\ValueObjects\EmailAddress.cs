using System;
using System.Text.RegularExpressions;
using System.Collections.Generic;

namespace EmailProcessor.Domain.ValueObjects
{
    /// <summary>
    /// Value object representing an email address with validation
    /// </summary>
    public class EmailAddress : IEquatable<EmailAddress>
    {
        public string Value { get; }

        private EmailAddress(string value)
        {
            Value = value;
        }

        public static EmailAddress Create(string emailAddress)
        {
            if (string.IsNullOrWhiteSpace(emailAddress))
                throw new ArgumentException("Email address cannot be null or empty", nameof(emailAddress));

            if (!IsValidEmail(emailAddress))
                throw new ArgumentException("Invalid email address format", nameof(emailAddress));

            return new EmailAddress(emailAddress.Trim().ToLowerInvariant());
        }

        public static bool IsValidEmail(string emailAddress)
        {
            if (string.IsNullOrWhiteSpace(emailAddress))
                return false;

            try
            {
                // Basic email validation regex
                var emailRegex = new Regex(@"^[^@\s]+@[^@\s]+\.[^@\s]+$");
                return emailRegex.IsMatch(emailAddress);
            }
            catch
            {
                return false;
            }
        }

        public override string ToString()
        {
            return Value;
        }

        public override bool Equals(object obj)
        {
            return Equals(obj as EmailAddress);
        }

        public bool Equals(EmailAddress other)
        {
            if (other is null) return false;
            if (ReferenceEquals(this, other)) return true;
            return Value == other.Value;
        }

        public override int GetHashCode()
        {
            return Value?.GetHashCode() ?? 0;
        }

        public static bool operator ==(EmailAddress left, EmailAddress right)
        {
            return EqualityComparer<EmailAddress>.Default.Equals(left, right);
        }

        public static bool operator !=(EmailAddress left, EmailAddress right)
        {
            return !(left == right);
        }

        public static implicit operator string(EmailAddress emailAddress)
        {
            return emailAddress?.Value;
        }
    }
} 