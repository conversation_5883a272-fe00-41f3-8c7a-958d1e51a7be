using System;
using System.Threading.Tasks;
using EmailProcessor.Domain.Interfaces;
using EmailProcessor.Domain.ValueObjects;

namespace EmailProcessor.Infrastructure.Logging
{
    public class SimpleLoggingProvider : ILoggingProvider
    {
        private LogLevel _currentLogLevel = LogLevel.Information;

        public LogLevel CurrentLogLevel => _currentLogLevel;

        public void SetLogLevel(LogLevel logLevel)
        {
            _currentLogLevel = logLevel;
        }

        public bool IsEnabled(LogLevel logLevel)
        {
            return logLevel >= _currentLogLevel;
        }

        public async Task LogAsync(LogLevel logLevel, string message, string sourceComponent, Exception exception = null, Guid? correlationId = null)
        {
            if (!IsEnabled(logLevel))
                return;

            var timestamp = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss.fff");
            var correlationInfo = correlationId.HasValue ? $" [CorrelationId: {correlationId}]" : "";
            var exceptionInfo = exception != null ? $" [Exception: {exception.Message}]" : "";
            
            var logMessage = $"{timestamp} [{logLevel}] [{sourceComponent}]{correlationInfo}: {message}{exceptionInfo}";
            
            // For now, just write to console
            Console.WriteLine(logMessage);
            
            if (exception != null)
            {
                Console.WriteLine($"Stack Trace: {exception.StackTrace}");
            }

            await Task.CompletedTask;
        }

        public async Task LogDebugAsync(string message, string sourceComponent, Guid? correlationId = null)
        {
            await LogAsync(LogLevel.Debug, message, sourceComponent, null, correlationId);
        }

        public async Task LogInformationAsync(string message, string sourceComponent, Guid? correlationId = null)
        {
            await LogAsync(LogLevel.Information, message, sourceComponent, null, correlationId);
        }

        public async Task LogWarningAsync(string message, string sourceComponent, Guid? correlationId = null)
        {
            await LogAsync(LogLevel.Warning, message, sourceComponent, null, correlationId);
        }

        public async Task LogErrorAsync(string message, string sourceComponent, Exception exception = null, Guid? correlationId = null)
        {
            await LogAsync(LogLevel.Error, message, sourceComponent, exception, correlationId);
        }

        public async Task LogFatalAsync(string message, string sourceComponent, Exception exception = null, Guid? correlationId = null)
        {
            await LogAsync(LogLevel.Fatal, message, sourceComponent, exception, correlationId);
        }
    }
} 