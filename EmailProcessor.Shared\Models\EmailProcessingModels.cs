using System.Text.Json.Serialization;

namespace EmailProcessor.Shared.Models
{
    /// <summary>
    /// Request model for email processing from VSTO add-in to Windows Service
    /// </summary>
    public class EmailProcessingRequest
    {
        [JsonPropertyName("messageType")]
        public string MessageType { get; set; } = "EmailProcessingRequest";

        [JsonPropertyName("correlationId")]
        public Guid CorrelationId { get; set; } = Guid.NewGuid();

        [JsonPropertyName("timestamp")]
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        [JsonPropertyName("data")]
        public EmailProcessingData Data { get; set; } = new EmailProcessingData();
    }

    /// <summary>
    /// Email processing data containing email and attachment information
    /// </summary>
    public class EmailProcessingData
    {
        [JsonPropertyName("email")]
        public EmailData Email { get; set; } = new EmailData();

        [JsonPropertyName("attachments")]
        public List<AttachmentData> Attachments { get; set; } = new List<AttachmentData>();
    }

    /// <summary>
    /// Email data structure
    /// </summary>
    public class EmailData
    {
        [JsonPropertyName("subject")]
        public string Subject { get; set; } = string.Empty;

        [JsonPropertyName("senderName")]
        public string SenderName { get; set; } = string.Empty;

        [JsonPropertyName("senderEmail")]
        public string SenderEmail { get; set; } = string.Empty;

        [JsonPropertyName("recipientTo")]
        public List<string> RecipientTo { get; set; } = new List<string>();

        [JsonPropertyName("recipientCC")]
        public List<string> RecipientCC { get; set; } = new List<string>();

        [JsonPropertyName("recipientBCC")]
        public List<string> RecipientBCC { get; set; } = new List<string>();

        [JsonPropertyName("emailType")]
        public string EmailType { get; set; } = "Received"; // "Received" or "Sent"

        [JsonPropertyName("timestamp")]
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        [JsonPropertyName("outlookMessageId")]
        public string OutlookMessageId { get; set; } = string.Empty;

        [JsonPropertyName("entryId")]
        public string EntryId { get; set; } = string.Empty;

        [JsonPropertyName("conversationId")]
        public string ConversationId { get; set; } = string.Empty;

        [JsonPropertyName("hasAttachments")]
        public bool HasAttachments { get; set; }

        [JsonPropertyName("attachmentCount")]
        public int AttachmentCount { get; set; }

        [JsonPropertyName("messageSize")]
        public long MessageSize { get; set; }

        [JsonPropertyName("isRead")]
        public bool IsRead { get; set; } = true;

        [JsonPropertyName("importance")]
        public string Importance { get; set; } = "Normal";

        [JsonPropertyName("categories")]
        public string Categories { get; set; } = string.Empty;

        [JsonPropertyName("sensitivity")]
        public string Sensitivity { get; set; } = "Normal";
    }

    /// <summary>
    /// Attachment data structure
    /// </summary>
    public class AttachmentData
    {
        [JsonPropertyName("fileName")]
        public string FileName { get; set; } = string.Empty;

        [JsonPropertyName("originalFileName")]
        public string OriginalFileName { get; set; } = string.Empty;

        [JsonPropertyName("contentType")]
        public string ContentType { get; set; } = string.Empty;

        [JsonPropertyName("fileExtension")]
        public string FileExtension { get; set; } = string.Empty;

        [JsonPropertyName("fileSize")]
        public long FileSize { get; set; }

        [JsonPropertyName("fileData")]
        public string FileData { get; set; } = string.Empty; // Base64 encoded file data
    }

    /// <summary>
    /// Response model for email processing results
    /// </summary>
    public class EmailProcessingResponse
    {
        [JsonPropertyName("messageType")]
        public string MessageType { get; set; } = "EmailProcessingResponse";

        [JsonPropertyName("correlationId")]
        public Guid CorrelationId { get; set; }

        [JsonPropertyName("timestamp")]
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        [JsonPropertyName("success")]
        public bool Success { get; set; }

        [JsonPropertyName("emailId")]
        public long? EmailId { get; set; }

        [JsonPropertyName("message")]
        public string Message { get; set; } = string.Empty;

        [JsonPropertyName("errorDetails")]
        public string? ErrorDetails { get; set; }

        [JsonPropertyName("processingResults")]
        public List<AttachmentProcessingResult> ProcessingResults { get; set; } = new List<AttachmentProcessingResult>();
    }

    /// <summary>
    /// Individual attachment processing result
    /// </summary>
    public class AttachmentProcessingResult
    {
        [JsonPropertyName("fileName")]
        public string FileName { get; set; } = string.Empty;

        [JsonPropertyName("attachmentId")]
        public long? AttachmentId { get; set; }

        [JsonPropertyName("success")]
        public bool Success { get; set; }

        [JsonPropertyName("localStoragePath")]
        public string? LocalStoragePath { get; set; }

        [JsonPropertyName("uncStoragePath")]
        public string? UncStoragePath { get; set; }

        [JsonPropertyName("errorMessage")]
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// Test connection message for health checks
    /// </summary>
    public class TestConnectionMessage
    {
        [JsonPropertyName("messageType")]
        public string MessageType { get; set; } = "TestConnection";

        [JsonPropertyName("correlationId")]
        public Guid CorrelationId { get; set; } = Guid.NewGuid();

        [JsonPropertyName("timestamp")]
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Test connection response
    /// </summary>
    public class TestConnectionResponse
    {
        [JsonPropertyName("messageType")]
        public string MessageType { get; set; } = "TestConnectionResponse";

        [JsonPropertyName("correlationId")]
        public Guid CorrelationId { get; set; }

        [JsonPropertyName("timestamp")]
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        [JsonPropertyName("success")]
        public bool Success { get; set; }

        [JsonPropertyName("message")]
        public string Message { get; set; } = string.Empty;

        [JsonPropertyName("serviceVersion")]
        public string ServiceVersion { get; set; } = "1.0.0";
    }
} 