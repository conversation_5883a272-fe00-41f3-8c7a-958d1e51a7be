using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using EmailProcessor.Domain.Entities;
using EmailProcessor.Domain.Interfaces;
using EmailProcessor.Domain.ValueObjects;
using EmailProcessor.Infrastructure.Database.Context;

namespace EmailProcessor.Infrastructure.Database.Repositories
{
    public class EmailRepository : IEmailRepository
    {
        private readonly EmailProcessorContext _context;

        public EmailRepository(EmailProcessorContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        public async Task<Email> AddAsync(Email email)
        {
            if (email == null)
                throw new ArgumentNullException(nameof(email));

            await _context.Emails.AddAsync(email);
            await _context.SaveChangesAsync();
            return email;
        }

        public async Task<Email> UpdateAsync(Email email)
        {
            if (email == null)
                throw new ArgumentNullException(nameof(email));

            _context.Emails.Update(email);
            await _context.SaveChangesAsync();
            return email;
        }

        public async Task<Email> GetByIdAsync(long emailId)
        {
            return await _context.Emails
                .Include(e => e.Attachments)
                .Include(e => e.ProcessingLogs)
                .FirstOrDefaultAsync(e => e.EmailId == emailId);
        }

        public async Task<Email> GetByOutlookMessageIdAsync(string outlookMessageId)
        {
            if (string.IsNullOrWhiteSpace(outlookMessageId))
                throw new ArgumentException("OutlookMessageId cannot be null or empty", nameof(outlookMessageId));

            return await _context.Emails
                .Include(e => e.Attachments)
                .Include(e => e.ProcessingLogs)
                .FirstOrDefaultAsync(e => e.OutlookMessageId == outlookMessageId);
        }

        public async Task<IEnumerable<Email>> GetByProcessingStatusAsync(ProcessingStatus status)
        {
            return await _context.Emails
                .Include(e => e.Attachments)
                .Where(e => e.ProcessingStatus == status)
                .OrderByDescending(e => e.Timestamp)
                .ToListAsync();
        }

        public async Task<IEnumerable<Email>> GetByEmailTypeAsync(EmailType emailType)
        {
            return await _context.Emails
                .Include(e => e.Attachments)
                .Where(e => e.EmailType == emailType)
                .OrderByDescending(e => e.Timestamp)
                .ToListAsync();
        }

        public async Task<IEnumerable<Email>> GetByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            if (startDate > endDate)
                throw new ArgumentException("StartDate cannot be greater than EndDate");

            return await _context.Emails
                .Include(e => e.Attachments)
                .Where(e => e.Timestamp >= startDate && e.Timestamp <= endDate)
                .OrderByDescending(e => e.Timestamp)
                .ToListAsync();
        }

        public async Task<IEnumerable<Email>> GetBySenderEmailAsync(string senderEmail)
        {
            if (string.IsNullOrWhiteSpace(senderEmail))
                throw new ArgumentException("SenderEmail cannot be null or empty", nameof(senderEmail));

            var emailAddress = EmailAddress.Create(senderEmail);
            return await _context.Emails
                .Include(e => e.Attachments)
                .Where(e => e.SenderEmail.Value == emailAddress.Value)
                .OrderByDescending(e => e.Timestamp)
                .ToListAsync();
        }

        public async Task<IEnumerable<Email>> GetByRecipientEmailAsync(string recipientEmail)
        {
            if (string.IsNullOrWhiteSpace(recipientEmail))
                throw new ArgumentException("RecipientEmail cannot be null or empty", nameof(recipientEmail));

            var emailAddress = EmailAddress.Create(recipientEmail);
            return await _context.Emails
                .Include(e => e.Attachments)
                .Where(e => e.RecipientTo.Any(r => r.Value == emailAddress.Value) || 
                           e.RecipientCC.Any(r => r.Value == emailAddress.Value))
                .OrderByDescending(e => e.Timestamp)
                .ToListAsync();
        }

        public async Task<bool> ExistsAsync(string outlookMessageId)
        {
            if (string.IsNullOrWhiteSpace(outlookMessageId))
                throw new ArgumentException("OutlookMessageId cannot be null or empty", nameof(outlookMessageId));

            return await _context.Emails.AnyAsync(e => e.OutlookMessageId == outlookMessageId);
        }

        public async Task DeleteAsync(long emailId)
        {
            var email = await GetByIdAsync(emailId);
            if (email != null)
            {
                _context.Emails.Remove(email);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<IEnumerable<Email>> GetAllAsync(
            EmailType? emailType = null,
            ProcessingStatus? processingStatus = null,
            DateTime? fromDate = null,
            DateTime? toDate = null)
        {
            var query = _context.Emails.Include(e => e.Attachments).AsQueryable();

            if (emailType.HasValue)
                query = query.Where(e => e.EmailType == emailType.Value);

            if (processingStatus.HasValue)
                query = query.Where(e => e.ProcessingStatus == processingStatus.Value);

            if (fromDate.HasValue)
                query = query.Where(e => e.Timestamp >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(e => e.Timestamp <= toDate.Value);

            return await query.OrderByDescending(e => e.Timestamp).ToListAsync();
        }

        public async Task<int> GetCountByProcessingStatusAsync(ProcessingStatus status)
        {
            return await _context.Emails.CountAsync(e => e.ProcessingStatus == status);
        }

        public async Task<int> GetCountByEmailTypeAsync(EmailType emailType)
        {
            return await _context.Emails.CountAsync(e => e.EmailType == emailType);
        }

        public async Task<IEnumerable<Email>> GetPendingEmailsAsync(int limit = 100)
        {
            return await _context.Emails
                .Include(e => e.Attachments)
                .Where(e => e.ProcessingStatus == ProcessingStatus.Pending)
                .OrderBy(e => e.Timestamp)
                .Take(limit)
                .ToListAsync();
        }

        public async Task<IEnumerable<Email>> GetFailedEmailsAsync(int limit = 100)
        {
            return await _context.Emails
                .Include(e => e.Attachments)
                .Where(e => e.ProcessingStatus == ProcessingStatus.Failed)
                .OrderByDescending(e => e.Timestamp)
                .Take(limit)
                .ToListAsync();
        }
    }
} 