using System;

namespace EmailProcessor.AddIn.Models
{
    /// <summary>
    /// Attachment data extracted from Outlook Attachment
    /// </summary>
    public class AttachmentData
    {
        public string FileName { get; set; } = string.Empty;
        public string OriginalFileName { get; set; } = string.Empty;
        public string ContentType { get; set; } = string.Empty;
        public string FileExtension { get; set; } = string.Empty;
        public long FileSize { get; set; }
        public string FileData { get; set; } = string.Empty; // Base64 encoded file data
        public string AttachmentId { get; set; } = string.Empty;
        public bool IsInline { get; set; }
        public string ContentId { get; set; } = string.Empty;
    }
} 