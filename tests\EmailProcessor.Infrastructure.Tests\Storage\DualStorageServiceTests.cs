using System;
using System.Threading.Tasks;
using EmailProcessor.Domain.Interfaces;
using EmailProcessor.Domain.ValueObjects;
using EmailProcessor.Infrastructure.Storage.DualStorageService;
using FluentAssertions;
using Moq;
using Xunit;

namespace EmailProcessor.Infrastructure.Tests.Storage
{
    public class DualStorageServiceTests
    {
        private readonly Mock<IStorageProvider> _mockLocalStorageProvider;
        private readonly Mock<IStorageProvider> _mockUncStorageProvider;
        private readonly Mock<ILoggingProvider> _mockLoggingProvider;
        private readonly DualStorageService _dualStorageService;

        public DualStorageServiceTests()
        {
            _mockLocalStorageProvider = new Mock<IStorageProvider>();
            _mockUncStorageProvider = new Mock<IStorageProvider>();
            _mockLoggingProvider = new Mock<ILoggingProvider>();

            // Setup storage provider types
            _mockLocalStorageProvider.Setup(x => x.StorageType).Returns(StorageType.Local);
            _mockUncStorageProvider.Setup(x => x.StorageType).Returns(StorageType.Unc);

            _dualStorageService = new DualStorageService(
                _mockLocalStorageProvider.Object,
                _mockUncStorageProvider.Object,
                _mockLoggingProvider.Object);
        }

        [Fact]
        public void Constructor_WithNullLocalStorageProvider_ThrowsArgumentNullException()
        {
            // Act & Assert
            var exception = Assert.Throws<ArgumentNullException>(() =>
                new DualStorageService(null, _mockUncStorageProvider.Object, _mockLoggingProvider.Object));

            exception.ParamName.Should().Be("localStorageProvider");
        }

        [Fact]
        public void Constructor_WithNullUncStorageProvider_ThrowsArgumentNullException()
        {
            // Act & Assert
            var exception = Assert.Throws<ArgumentNullException>(() =>
                new DualStorageService(_mockLocalStorageProvider.Object, null, _mockLoggingProvider.Object));

            exception.ParamName.Should().Be("uncStorageProvider");
        }

        [Fact]
        public void Constructor_WithNullLoggingProvider_ThrowsArgumentNullException()
        {
            // Act & Assert
            var exception = Assert.Throws<ArgumentNullException>(() =>
                new DualStorageService(_mockLocalStorageProvider.Object, _mockUncStorageProvider.Object, null));

            exception.ParamName.Should().Be("loggingProvider");
        }

        [Fact]
        public void Constructor_WithWrongStorageTypes_ThrowsArgumentException()
        {
            // Arrange
            var wrongTypeProvider = new Mock<IStorageProvider>();
            wrongTypeProvider.Setup(x => x.StorageType).Returns(StorageType.Unc); // Should be Local

            // Act & Assert
            var exception = Assert.Throws<ArgumentException>(() =>
                new DualStorageService(wrongTypeProvider.Object, _mockUncStorageProvider.Object, _mockLoggingProvider.Object));

            exception.ParamName.Should().Be("localStorageProvider");
            exception.Message.Should().Contain("First storage provider must be Local type");
        }

        [Fact]
        public async Task SaveFileAsync_WithByteArray_BothStorageSucceed_ReturnsSuccessfulResult()
        {
            // Arrange
            var fileName = "test.txt";
            var fileData = new byte[] { 1, 2, 3, 4, 5 };
            var subDirectory = "testdir";

            var localFilePath = FilePath.Create(@"C:\local\path\test.txt");
            var uncFilePath = FilePath.Create(@"\\server\share\path\test.txt");

            _mockLocalStorageProvider
                .Setup(x => x.SaveFileAsync(fileData, fileName, subDirectory))
                .ReturnsAsync(localFilePath);

            _mockLocalStorageProvider
                .Setup(x => x.GetFileInfoAsync(localFilePath.Value))
                .ReturnsAsync(new System.IO.FileInfo(@"C:\temp\test.txt"));

            _mockUncStorageProvider
                .Setup(x => x.SaveFileAsync(fileData, fileName, subDirectory))
                .ReturnsAsync(uncFilePath);

            _mockUncStorageProvider
                .Setup(x => x.GetFileInfoAsync(uncFilePath.Value))
                .ReturnsAsync(new System.IO.FileInfo(@"C:\temp\test.txt"));

            // Act
            var result = await _dualStorageService.SaveFileAsync(fileName, fileData, subDirectory);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccessful.Should().BeTrue();
            result.FileName.Should().Be(fileName);
            result.SubDirectory.Should().Be(subDirectory);
            result.LocalStorageResult.Should().NotBeNull();
            result.UncStorageResult.Should().NotBeNull();
            result.LocalStorageResult.IsSuccessful.Should().BeTrue();
            result.UncStorageResult.IsSuccessful.Should().BeTrue();
            result.LocalStorageResult.StorageType.Should().Be(StorageType.Local);
            result.UncStorageResult.StorageType.Should().Be(StorageType.Unc);
        }

        [Fact]
        public async Task SaveFileAsync_WithByteArray_LocalStorageFails_UncStorageSucceeds_ReturnsPartialSuccess()
        {
            // Arrange
            var fileName = "test.txt";
            var fileData = new byte[] { 1, 2, 3, 4, 5 };
            var subDirectory = "testdir";

            var uncFilePath = FilePath.Create(@"\\server\share\path\test.txt");

            _mockLocalStorageProvider
                .Setup(x => x.SaveFileAsync(fileData, fileName, subDirectory))
                .ThrowsAsync(new System.IO.IOException("Local storage error"));

            _mockUncStorageProvider
                .Setup(x => x.SaveFileAsync(fileData, fileName, subDirectory))
                .ReturnsAsync(uncFilePath);

            _mockUncStorageProvider
                .Setup(x => x.GetFileInfoAsync(uncFilePath.Value))
                .ReturnsAsync(new System.IO.FileInfo(@"C:\temp\test.txt"));

            // Act
            var result = await _dualStorageService.SaveFileAsync(fileName, fileData, subDirectory);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccessful.Should().BeTrue(); // Overall success because UNC succeeded
            result.LocalStorageResult.IsSuccessful.Should().BeFalse();
            result.UncStorageResult.IsSuccessful.Should().BeTrue();
            result.LocalStorageResult.ErrorMessage.Should().Contain("Local storage error");
        }

        [Fact]
        public async Task SaveFileAsync_WithByteArray_BothStoragesFail_ReturnsFailureResult()
        {
            // Arrange
            var fileName = "test.txt";
            var fileData = new byte[] { 1, 2, 3, 4, 5 };
            var subDirectory = "testdir";

            _mockLocalStorageProvider
                .Setup(x => x.SaveFileAsync(fileData, fileName, subDirectory))
                .ThrowsAsync(new System.IO.IOException("Local storage error"));

            _mockUncStorageProvider
                .Setup(x => x.SaveFileAsync(fileData, fileName, subDirectory))
                .ThrowsAsync(new System.IO.IOException("UNC storage error"));

            // Act
            var result = await _dualStorageService.SaveFileAsync(fileName, fileData, subDirectory);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccessful.Should().BeFalse();
            result.LocalStorageResult.IsSuccessful.Should().BeFalse();
            result.UncStorageResult.IsSuccessful.Should().BeFalse();
            result.LocalStorageResult.ErrorMessage.Should().Contain("Local storage error");
            result.UncStorageResult.ErrorMessage.Should().Contain("UNC storage error");
        }

        [Fact]
        public async Task SaveFileAsync_WithStream_BothStorageSucceed_ReturnsSuccessfulResult()
        {
            // Arrange
            var fileName = "test.txt";
            var subDirectory = "testdir";
            var localFilePath = FilePath.Create(@"C:\local\path\test.txt");
            var uncFilePath = FilePath.Create(@"\\server\share\path\test.txt");

            using var fileStream = new System.IO.MemoryStream(new byte[] { 1, 2, 3, 4, 5 });

            _mockLocalStorageProvider
                .Setup(x => x.SaveFileAsync(It.IsAny<byte[]>(), fileName, subDirectory))
                .ReturnsAsync(localFilePath);

            _mockLocalStorageProvider
                .Setup(x => x.GetFileInfoAsync(localFilePath.Value))
                .ReturnsAsync(new System.IO.FileInfo(@"C:\temp\test.txt"));

            _mockUncStorageProvider
                .Setup(x => x.SaveFileAsync(It.IsAny<byte[]>(), fileName, subDirectory))
                .ReturnsAsync(uncFilePath);

            _mockUncStorageProvider
                .Setup(x => x.GetFileInfoAsync(uncFilePath.Value))
                .ReturnsAsync(new System.IO.FileInfo(@"C:\temp\test.txt"));

            // Act
            var result = await _dualStorageService.SaveFileAsync(fileName, fileStream, subDirectory);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccessful.Should().BeTrue();
            result.FileName.Should().Be(fileName);
            result.SubDirectory.Should().Be(subDirectory);
        }

        [Fact]
        public async Task TestConnectivityAsync_BothStoragesConnected_ReturnsTrue()
        {
            // Arrange
            _mockLocalStorageProvider
                .Setup(x => x.TestConnectivityAsync())
                .ReturnsAsync(true);

            _mockUncStorageProvider
                .Setup(x => x.TestConnectivityAsync())
                .ReturnsAsync(true);

            // Act
            var result = await _dualStorageService.TestConnectivityAsync();

            // Assert
            result.Should().BeTrue();
        }

        [Fact]
        public async Task TestConnectivityAsync_LocalStorageDisconnected_ReturnsFalse()
        {
            // Arrange
            _mockLocalStorageProvider
                .Setup(x => x.TestConnectivityAsync())
                .ReturnsAsync(false);

            _mockUncStorageProvider
                .Setup(x => x.TestConnectivityAsync())
                .ReturnsAsync(true);

            // Act
            var result = await _dualStorageService.TestConnectivityAsync();

            // Assert
            result.Should().BeFalse();
        }

        [Fact]
        public async Task GetStorageHealthStatusAsync_ReturnsCorrectStatus()
        {
            // Arrange
            var localAvailableSpace = 1000000L;
            var uncAvailableSpace = 2000000L;

            _mockLocalStorageProvider
                .Setup(x => x.TestConnectivityAsync())
                .ReturnsAsync(true);

            _mockLocalStorageProvider
                .Setup(x => x.GetAvailableSpaceAsync())
                .ReturnsAsync(localAvailableSpace);

            _mockUncStorageProvider
                .Setup(x => x.TestConnectivityAsync())
                .ReturnsAsync(true);

            _mockUncStorageProvider
                .Setup(x => x.GetAvailableSpaceAsync())
                .ReturnsAsync(uncAvailableSpace);

            // Act
            var result = await _dualStorageService.GetStorageHealthStatusAsync();

            // Assert
            result.Should().NotBeNull();
            result.LocalStorageAvailable.Should().BeTrue();
            result.UncStorageAvailable.Should().BeTrue();
            result.LocalAvailableSpace.Should().Be(localAvailableSpace);
            result.UncAvailableSpace.Should().Be(uncAvailableSpace);
            result.Timestamp.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
        }

        [Fact]
        public async Task SaveFileAsync_LogsInformationMessages()
        {
            // Arrange
            var fileName = "test.txt";
            var fileData = new byte[] { 1, 2, 3, 4, 5 };
            var localFilePath = FilePath.Create(@"C:\local\path\test.txt");
            var uncFilePath = FilePath.Create(@"\\server\share\path\test.txt");

            _mockLocalStorageProvider
                .Setup(x => x.SaveFileAsync(fileData, fileName, null))
                .ReturnsAsync(localFilePath);

            _mockLocalStorageProvider
                .Setup(x => x.GetFileInfoAsync(localFilePath.Value))
                .ReturnsAsync(new System.IO.FileInfo(@"C:\temp\test.txt"));

            _mockUncStorageProvider
                .Setup(x => x.SaveFileAsync(fileData, fileName, null))
                .ReturnsAsync(uncFilePath);

            _mockUncStorageProvider
                .Setup(x => x.GetFileInfoAsync(uncFilePath.Value))
                .ReturnsAsync(new System.IO.FileInfo(@"C:\temp\test.txt"));

            // Act
            await _dualStorageService.SaveFileAsync(fileName, fileData);

            // Assert - Verify logging calls were made
            _mockLoggingProvider.Verify(
                x => x.LogInformation(
                    It.Is<string>(msg => msg.Contains("Dual storage save completed")),
                    It.IsAny<string>(),
                    It.IsAny<Guid?>()),
                Times.Once);
        }
    }
}