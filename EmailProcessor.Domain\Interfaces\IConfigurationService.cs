using System.Collections.Generic;
using System.Threading.Tasks;

namespace EmailProcessor.Domain.Interfaces
{
    /// <summary>
    /// Interface for configuration management services
    /// </summary>
    public interface IConfigurationService
    {
        /// <summary>
        /// Gets a configuration value by key
        /// </summary>
        Task<T> GetConfigurationAsync<T>(string key, T defaultValue = default);

        /// <summary>
        /// Sets a configuration value by key
        /// </summary>
        Task SetConfigurationAsync<T>(string key, T value, string description = null, bool isEncrypted = false);

        /// <summary>
        /// Deletes a configuration value by key
        /// </summary>
        Task<bool> DeleteConfigurationAsync(string key);

        /// <summary>
        /// Gets all configuration values
        /// </summary>
        Task<Dictionary<string, object>> GetAllConfigurationsAsync();

        /// <summary>
        /// Initializes default configuration values if they don't exist
        /// </summary>
        Task InitializeDefaultConfigurationAsync();

        /// <summary>
        /// Clears the configuration cache
        /// </summary>
        void ClearCache();
    }
}