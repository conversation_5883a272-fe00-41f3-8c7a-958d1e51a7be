using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EmailProcessor.Domain.Entities;
using EmailProcessor.Domain.ValueObjects;

namespace EmailProcessor.Domain.Interfaces
{
    /// <summary>
    /// Repository interface for Attachment entities
    /// </summary>
    public interface IAttachmentRepository
    {
        /// <summary>
        /// Gets an attachment by its ID
        /// </summary>
        Task<Attachment> GetByIdAsync(long attachmentId);

        /// <summary>
        /// Gets all attachments for a specific email
        /// </summary>
        Task<IEnumerable<Attachment>> GetByEmailIdAsync(long emailId);

        /// <summary>
        /// Gets attachments by processing status
        /// </summary>
        Task<IEnumerable<Attachment>> GetByProcessingStatusAsync(ProcessingStatus status);

        /// <summary>
        /// Gets attachments by local processing status
        /// </summary>
        Task<IEnumerable<Attachment>> GetByLocalProcessingStatusAsync(ProcessingStatus status);

        /// <summary>
        /// Gets attachments by UNC processing status
        /// </summary>
        Task<IEnumerable<Attachment>> GetByUncProcessingStatusAsync(ProcessingStatus status);

        /// <summary>
        /// Gets attachments by file extension
        /// </summary>
        Task<IEnumerable<Attachment>> GetByFileExtensionAsync(string fileExtension);

        /// <summary>
        /// Gets attachments with processing errors
        /// </summary>
        Task<IEnumerable<Attachment>> GetWithProcessingErrorsAsync();

        /// <summary>
        /// Gets attachments that are fully processed
        /// </summary>
        Task<IEnumerable<Attachment>> GetFullyProcessedAsync();

        /// <summary>
        /// Adds a new attachment
        /// </summary>
        Task<Attachment> AddAsync(Attachment attachment);

        /// <summary>
        /// Updates an existing attachment
        /// </summary>
        Task<Attachment> UpdateAsync(Attachment attachment);

        /// <summary>
        /// Deletes an attachment
        /// </summary>
        Task DeleteAsync(long attachmentId);

        /// <summary>
        /// Gets the count of attachments with a specific processing status
        /// </summary>
        Task<int> GetCountByProcessingStatusAsync(ProcessingStatus status);

        /// <summary>
        /// Gets the count of attachments by file extension
        /// </summary>
        Task<int> GetCountByFileExtensionAsync(string fileExtension);

        /// <summary>
        /// Gets the total size of all attachments
        /// </summary>
        Task<long> GetTotalSizeAsync();

        /// <summary>
        /// Gets the total size of attachments for a specific email
        /// </summary>
        Task<long> GetTotalSizeByEmailIdAsync(long emailId);
    }
} 