using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using EmailProcessor.Domain.Interfaces;
using EmailProcessor.Infrastructure.Logging;

namespace EmailProcessor.Infrastructure.Logging
{
    /// <summary>
    /// Service for managing log files, including cleanup and rotation policies
    /// </summary>
    public class LogManagementService : ILogManagementService
    {
        private readonly ILoggingProvider _loggingProvider;
        private readonly LogManagementConfiguration _configuration;

        public LogManagementService(LogManagementConfiguration configuration = null, ILoggingProvider loggingProvider = null)
        {
            _configuration = configuration ?? new LogManagementConfiguration();
            _loggingProvider = loggingProvider;
        }

        /// <summary>
        /// Performs log cleanup based on retention policies
        /// </summary>
        public async Task PerformLogCleanupAsync()
        {
            try
            {
                _loggingProvider?.LogInformation("Starting log cleanup process");

                var tasksToRun = new List<Task>();

                // Clean up log files by age
                if (_configuration.RetentionDays > 0)
                {
                    tasksToRun.Add(CleanupLogFilesByAgeAsync());
                }

                // Clean up log files by size
                if (_configuration.MaxTotalLogSizeBytes > 0)
                {
                    tasksToRun.Add(CleanupLogFilesBySizeAsync());
                }

                // Clean up log files by count
                if (_configuration.MaxLogFileCount > 0)
                {
                    tasksToRun.Add(CleanupLogFilesByCountAsync());
                }

                // Run all cleanup tasks in parallel
                await Task.WhenAll(tasksToRun);

                _loggingProvider?.LogInformation("Log cleanup process completed");
            }
            catch (Exception ex)
            {
                _loggingProvider?.LogError("Error during log cleanup process", ex);
                throw;
            }
        }

        /// <summary>
        /// Gets log file statistics
        /// </summary>
        public async Task<LogStatistics> GetLogStatisticsAsync()
        {
            try
            {
                var logFiles = GetLogFiles();
                var statistics = new LogStatistics
                {
                    TotalLogFiles = logFiles.Count,
                    TotalLogSizeBytes = logFiles.Sum(f => f.Length),
                    OldestLogFile = logFiles.OrderBy(f => f.CreationTime).FirstOrDefault()?.CreationTime,
                    NewestLogFile = logFiles.OrderByDescending(f => f.CreationTime).FirstOrDefault()?.CreationTime,
                    LogDirectory = _configuration.LogDirectory
                };

                await Task.CompletedTask;
                return statistics;
            }
            catch (Exception ex)
            {
                _loggingProvider?.LogError("Error retrieving log statistics", ex);
                return new LogStatistics { LogDirectory = _configuration.LogDirectory };
            }
        }

        /// <summary>
        /// Archives old log files to a compressed format
        /// </summary>
        public async Task ArchiveOldLogsAsync()
        {
            try
            {
                if (!_configuration.EnableArchiving)
                    return;

                _loggingProvider?.LogInformation("Starting log archival process");

                var logFiles = GetLogFiles()
                    .Where(f => f.CreationTime < DateTime.Now.AddDays(-_configuration.ArchiveAfterDays))
                    .OrderBy(f => f.CreationTime)
                    .ToList();

                if (!logFiles.Any())
                {
                    _loggingProvider?.LogDebug("No log files found for archival");
                    return;
                }

                var archiveDirectory = Path.Combine(_configuration.LogDirectory, "Archive");
                if (!Directory.Exists(archiveDirectory))
                {
                    Directory.CreateDirectory(archiveDirectory);
                }

                foreach (var logFile in logFiles)
                {
                    try
                    {
                        var archiveFileName = $"{Path.GetFileNameWithoutExtension(logFile.Name)}_{logFile.CreationTime:yyyyMMdd}.zip";
                        var archivePath = Path.Combine(archiveDirectory, archiveFileName);

                        // Create compressed archive (simplified - in production would use System.IO.Compression)
                        File.Move(logFile.FullName, archivePath.Replace(".zip", ".log"));
                        
                        _loggingProvider?.LogDebug($"Archived log file: {logFile.Name} to {archivePath}");
                    }
                    catch (Exception ex)
                    {
                        _loggingProvider?.LogError($"Error archiving log file {logFile.Name}", ex);
                    }
                }

                _loggingProvider?.LogInformation($"Log archival completed. {logFiles.Count} files archived.");
            }
            catch (Exception ex)
            {
                _loggingProvider?.LogError("Error during log archival process", ex);
                throw;
            }

            await Task.CompletedTask;
        }

        /// <summary>
        /// Validates log configuration and directory structure
        /// </summary>
        public async Task<LogValidationResult> ValidateLogConfigurationAsync()
        {
            var result = new LogValidationResult();

            try
            {
                // Check if log directory exists and is writable
                if (!Directory.Exists(_configuration.LogDirectory))
                {
                    try
                    {
                        Directory.CreateDirectory(_configuration.LogDirectory);
                        result.DirectoryAccessible = true;
                    }
                    catch (Exception ex)
                    {
                        result.DirectoryAccessible = false;
                        result.ValidationErrors.Add($"Cannot create log directory: {ex.Message}");
                    }
                }
                else
                {
                    result.DirectoryAccessible = true;
                }

                // Test write permissions
                if (result.DirectoryAccessible)
                {
                    try
                    {
                        var testFile = Path.Combine(_configuration.LogDirectory, $"test_{Guid.NewGuid()}.tmp");
                        await File.WriteAllTextAsync(testFile, "test");
                        File.Delete(testFile);
                        result.DirectoryWritable = true;
                    }
                    catch (Exception ex)
                    {
                        result.DirectoryWritable = false;
                        result.ValidationErrors.Add($"Cannot write to log directory: {ex.Message}");
                    }
                }

                // Check available disk space
                try
                {
                    var driveInfo = new DriveInfo(Path.GetPathRoot(_configuration.LogDirectory));
                    result.AvailableDiskSpaceBytes = driveInfo.AvailableFreeSpace;
                    
                    if (result.AvailableDiskSpaceBytes < _configuration.MinimumDiskSpaceBytes)
                    {
                        result.ValidationErrors.Add($"Insufficient disk space. Available: {result.AvailableDiskSpaceBytes}, Required: {_configuration.MinimumDiskSpaceBytes}");
                    }
                }
                catch (Exception ex)
                {
                    result.ValidationErrors.Add($"Cannot determine disk space: {ex.Message}");
                }

                // Validate configuration values
                if (_configuration.RetentionDays <= 0 && _configuration.MaxLogFileCount <= 0 && _configuration.MaxTotalLogSizeBytes <= 0)
                {
                    result.ValidationWarnings.Add("No retention policies configured - logs will grow indefinitely");
                }

                result.IsValid = !result.ValidationErrors.Any();
            }
            catch (Exception ex)
            {
                result.IsValid = false;
                result.ValidationErrors.Add($"Validation failed: {ex.Message}");
            }

            return result;
        }

        private async Task CleanupLogFilesByAgeAsync()
        {
            try
            {
                var cutoffDate = DateTime.Now.AddDays(-_configuration.RetentionDays);
                var oldFiles = GetLogFiles()
                    .Where(f => f.CreationTime < cutoffDate)
                    .ToList();

                foreach (var file in oldFiles)
                {
                    try
                    {
                        file.Delete();
                        _loggingProvider?.LogDebug($"Deleted old log file: {file.Name}");
                    }
                    catch (Exception ex)
                    {
                        _loggingProvider?.LogError($"Error deleting old log file {file.Name}", ex);
                    }
                }

                if (oldFiles.Any())
                {
                    _loggingProvider?.LogInformation($"Cleaned up {oldFiles.Count} log files by age (older than {_configuration.RetentionDays} days)");
                }
            }
            catch (Exception ex)
            {
                _loggingProvider?.LogError("Error during age-based log cleanup", ex);
            }

            await Task.CompletedTask;
        }

        private async Task CleanupLogFilesBySizeAsync()
        {
            try
            {
                var logFiles = GetLogFiles().OrderBy(f => f.CreationTime).ToList();
                var totalSize = logFiles.Sum(f => f.Length);

                while (totalSize > _configuration.MaxTotalLogSizeBytes && logFiles.Any())
                {
                    var oldestFile = logFiles.First();
                    try
                    {
                        totalSize -= oldestFile.Length;
                        oldestFile.Delete();
                        logFiles.Remove(oldestFile);
                        _loggingProvider?.LogDebug($"Deleted log file for size limit: {oldestFile.Name}");
                    }
                    catch (Exception ex)
                    {
                        _loggingProvider?.LogError($"Error deleting log file {oldestFile.Name} for size limit", ex);
                        break;
                    }
                }

                _loggingProvider?.LogInformation($"Size-based cleanup completed. Total log size: {totalSize} bytes");
            }
            catch (Exception ex)
            {
                _loggingProvider?.LogError("Error during size-based log cleanup", ex);
            }

            await Task.CompletedTask;
        }

        private async Task CleanupLogFilesByCountAsync()
        {
            try
            {
                var logFiles = GetLogFiles().OrderBy(f => f.CreationTime).ToList();

                while (logFiles.Count > _configuration.MaxLogFileCount)
                {
                    var oldestFile = logFiles.First();
                    try
                    {
                        oldestFile.Delete();
                        logFiles.Remove(oldestFile);
                        _loggingProvider?.LogDebug($"Deleted log file for count limit: {oldestFile.Name}");
                    }
                    catch (Exception ex)
                    {
                        _loggingProvider?.LogError($"Error deleting log file {oldestFile.Name} for count limit", ex);
                        break;
                    }
                }

                _loggingProvider?.LogInformation($"Count-based cleanup completed. Remaining log files: {logFiles.Count}");
            }
            catch (Exception ex)
            {
                _loggingProvider?.LogError("Error during count-based log cleanup", ex);
            }

            await Task.CompletedTask;
        }

        private List<FileInfo> GetLogFiles()
        {
            try
            {
                if (!Directory.Exists(_configuration.LogDirectory))
                    return new List<FileInfo>();

                var directory = new DirectoryInfo(_configuration.LogDirectory);
                return directory.GetFiles(_configuration.LogFilePattern)
                    .Where(f => !f.Name.Contains("test_")) // Exclude test files
                    .ToList();
            }
            catch (Exception ex)
            {
                _loggingProvider?.LogError($"Error retrieving log files from {_configuration.LogDirectory}", ex);
                return new List<FileInfo>();
            }
        }
    }

    /// <summary>
    /// Configuration for log management operations
    /// </summary>
    public class LogManagementConfiguration
    {
        /// <summary>
        /// Directory where log files are stored
        /// </summary>
        public string LogDirectory { get; set; } = Path.Combine("Logs", "EmailProcessor");

        /// <summary>
        /// Pattern to match log files
        /// </summary>
        public string LogFilePattern { get; set; } = "*.log";

        /// <summary>
        /// Number of days to retain log files (0 = no age-based cleanup)
        /// </summary>
        public int RetentionDays { get; set; } = 30;

        /// <summary>
        /// Maximum total size of all log files in bytes (0 = no size-based cleanup)
        /// </summary>
        public long MaxTotalLogSizeBytes { get; set; } = 1024 * 1024 * 1024; // 1GB

        /// <summary>
        /// Maximum number of log files to keep (0 = no count-based cleanup)
        /// </summary>
        public int MaxLogFileCount { get; set; } = 100;

        /// <summary>
        /// Enable archiving of old log files
        /// </summary>
        public bool EnableArchiving { get; set; } = false;

        /// <summary>
        /// Archive log files older than this many days
        /// </summary>
        public int ArchiveAfterDays { get; set; } = 7;

        /// <summary>
        /// Minimum free disk space required in bytes
        /// </summary>
        public long MinimumDiskSpaceBytes { get; set; } = 500 * 1024 * 1024; // 500MB
    }

    /// <summary>
    /// Statistics about log files
    /// </summary>
    public class LogStatistics
    {
        public int TotalLogFiles { get; set; }
        public long TotalLogSizeBytes { get; set; }
        public DateTime? OldestLogFile { get; set; }
        public DateTime? NewestLogFile { get; set; }
        public string LogDirectory { get; set; }
    }

    /// <summary>
    /// Result of log configuration validation
    /// </summary>
    public class LogValidationResult
    {
        public bool IsValid { get; set; }
        public bool DirectoryAccessible { get; set; }
        public bool DirectoryWritable { get; set; }
        public long AvailableDiskSpaceBytes { get; set; }
        public List<string> ValidationErrors { get; set; } = new List<string>();
        public List<string> ValidationWarnings { get; set; } = new List<string>();
    }
}