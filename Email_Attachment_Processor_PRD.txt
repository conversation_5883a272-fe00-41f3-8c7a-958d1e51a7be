--- START OF FILE Email_Attachment_Processor_PRD_v1.2.txt ---

# Product Requirements Document: Email Attachment Processor - Version 1.2

## 1. Introduction & Purpose

### 1.1 Product Name: Email Attachment Processor

### 1.2 Product Goal: To automatically and reliably process email attachments from Microsoft Outlook, save them to designated storage locations (local and remote UNC shares), and record relevant email and attachment metadata in a SQL database. The system operates on the same machine where Out<PERSON> is installed, with the VSTO add-in communicating with a local Windows Service for attachment processing. This system aims to streamline email attachment management and improve data accessibility.

### 1.3 Target Audience:

*   **End Users:** Employees who need to automatically archive and access email attachments.
*   **IT Administrators:** Responsible for deployment, configuration, and maintenance of the Email Attachment Processor.

## 2. Goals and Objectives

### 2.1 Business Objectives:

*   **Improve Efficiency:** Reduce manual effort in saving and organizing email attachments.
*   **Enhance Data Accessibility:** Provide a centralized and searchable repository for email attachments.
*   **Ensure Data Compliance:** Facilitate compliance with data retention and archival policies (as applicable).

### 2.2 Product Objectives:

*   **Reliable Attachment Processing:** Achieve a high success rate in processing and saving email attachments.
*   **Efficient Performance:** Process emails and attachments with minimal latency, ensuring a non-disruptive user experience.
*   **Robust Error Handling:** Implement comprehensive error handling and logging for easy troubleshooting.
*   **Configurability and Flexibility:** Allow administrators to configure storage locations, directory structures, logging levels, and other system parameters.
*   **Maintainability and Extensibility:** Design a clean and modular architecture for easy maintenance and future feature additions.

### 2.3 Success Metrics:

*   **Processing Success Rate:** Percentage of emails and attachments processed and saved successfully (target: > 99%).
*   **Processing Latency:** Time taken to process an email and its attachments from Outlook event trigger to database insertion (target: within acceptable limits, e.g., < 2 seconds for emails with typical attachment sizes).
*   **Data Accuracy:** Accuracy of metadata extracted and stored in the database (target: 100% accuracy of extracted metadata).
*   **System Uptime:** Availability of the Email Attachment Processor (target: > 99% uptime).
*   **User Satisfaction:** Positive feedback from end-users and reduced IT support requests related to email attachment management.

## 3. Features

### 3.1 Core Features:
#### 3.1.1 Outlook Add-in Integration:

*   **3.1.1.1 Seamless Outlook Integration:** Seamlessly integrates with Microsoft Outlook (desktop application).
*   **3.1.1.2 .NET Framework 4.8 Compatibility:** Supports .NET Framework 4.8 compatibility for Outlook Add-in requirements.
*   ********* Outlook Event Monitoring:** Monitors Outlook events:
    *   `NewMailEx` event for received emails.
    *   `ItemSend` event for sent emails.
*   ********* Event-Triggered Processing:** Triggers processing upon detection of relevant email events.

#### 3.1.2 Email Data Extraction:

*   ********* Metadata Extraction:** Extracts relevant email metadata from Outlook `MailItem` objects:
    *   Subject
    *   Sender (Name, Email Address) for received emails
    *   Recipient (To, CC) for sent emails
    *   Timestamp (Sent/Received Date)
    *   Attachment information
*   ********* Domain Entity Mapping:** Maps Outlook-specific email data to Core Domain Entities (`Email`, `Document`).

#### 3.1.3 Attachment Processing & Saving:

*   ********* Attachment Processing:** Processes and saves email attachments.
*   ********* Storage Destinations:** Saves attachments to BOTH destinations simultaneously:
    *   Local file system paths (primary local storage).
    *   Remote UNC network shares (backup/network storage).
*   ********* Dynamic Directory Structure:** Dynamically creates directory structure based on configuration: `Year/Month/Day/EmailType (Received/Sent)`.
*   ********* Automatic Directory Creation:** Handles directory creation automatically if directories do not exist.
*   ********* Original File Saving:** Saves attachments with their original file names and extensions.
*   ********* Attachment Saving Logging:** Logs successful and failed attachment saving operations.

#### 3.1.4 Database Integration:

*   ********* .NET 8 & .NET Core Principles:** Uses .NET 8 and .NET Core principles for database interactions.
*   ********* SQL Server Support:** Supports SQL Server with comprehensive schema design.
*   ********* Repository Pattern:** Implements Repository Pattern for data access abstraction.
*   ********* Metadata Storage:** Saves the following information to the database:
    *   **Emails Table**: Subject, Timestamp, Sender/Recipient details, Email Type, Outlook Message ID, Processing Status
    *   **Attachments Table**: FileName, ContentType, Size, FileExtension, LocalStoragePath, UncStoragePath, LocalProcessingStatus, UncProcessingStatus
    *   **ProcessingLogs Table**: Log entries with correlation IDs for tracing
    *   **Configuration Table**: System configuration settings
*   ********* Data Integrity:** Ensures data integrity through foreign key constraints, indexes, and validation rules.
*   ********* Performance Optimization:** Optimized indexing strategy for fast query performance and efficient data retrieval.

#### 3.1.5 Logging:

*   ********* Comprehensive Logging:** Comprehensive logging of system activities, errors, warnings, and informational messages.
*   ********* Robust Logging Library:** Uses a robust logging library (e.g., Serilog, NLog).
*   ********* Log Destinations:** Logs to:
    *   File (configurable log file path).
    *   Optionally, Console (for debugging/development).
*   **3.1.5.4 Configurable Logging Levels:** Configurable logging levels (e.g., Debug, Information, Warning, Error, Fatal).
*   **3.1.5.5 Detailed Log Information:** Includes timestamps, source component, log level, and message details in logs.

#### 3.1.6 Configuration:

*   **3.1.6.1 Externalized Configuration:** Externalized configuration (using configuration files - `appsettings.json` for .NET 8, `.config` for .NET Framework 4.8).
*   **3.1.6.2 Configurable Parameters:** Configurable parameters:
    *   Base local storage path for attachments.
    *   Base UNC storage path for attachments.
    *   Directory structure configuration (Year/Month/Day/EmailType).
    *   Database connection string.
    *   Logging level and output destinations.
    *   Attachment file size limits (if any).
    *   Email types to process (Received, Sent, or both – configurable filtering).

#### 3.1.7 Error Handling and Resilience:

*   **3.1.7.1 Graceful Error Handling:** Graceful error handling for common scenarios (e.g., network issues, file system errors, database connection problems).
*   **3.1.7.2 Retry Mechanism:** Retries mechanism with configurable exponential backoff for transient errors (e.g., temporary network glitches).
*   **3.1.7.3 Detailed Error Logging:** Detailed error logging with context information to diagnose and resolve issues quickly.

### 3.2 Non-Functional Requirements:
#### 3.2.1 Performance:

*   ********* Responsiveness:** Minimal impact on Outlook performance. Attachment processing should occur in the background without blocking user interaction in Outlook.
*   ********* Throughput:** Capable of processing a high volume of emails and attachments efficiently.
*   ********* Scalability:** Designed to handle increasing email volume and attachment sizes within the target organization.

#### 3.2.2 Reliability:

*   ********* Data Integrity:** Ensure data integrity throughout the processing pipeline, from email extraction to database storage and file saving, including robust error handling and data validation.
*   ********* Fault Tolerance:** Resilient to common failures (e.g., temporary network outages, storage system issues, database failures).
*   ********* Stability:** Stable and dependable operation with minimal crashes or unexpected behavior.

#### 3.2.3 Security:

*   ********* Data Security:** Handle sensitive email and attachment data with appropriate security measures.
*   ********* Access Control:** Implement access control to configuration files, log files, and the database.
*   ********* Auditability:** Maintain logs of system activities for security auditing and compliance purposes.

#### 3.2.4 Maintainability:

*   ********* Code Quality:** Write clean, well-documented, and modular code adhering to SOLID principles and coding standards.
*   ********* Testability:** Design the architecture for high testability, enabling comprehensive unit tests and integration tests with clear separation of concerns.
*   ********* Modularity:** Employ Clean Architecture principles with SOLID design to ensure highly modular and loosely coupled components.
*   ********* DRY Compliance:** Eliminate code duplication through shared components and reusable abstractions.
*   ********* Scalable Structure:** Maintain a folder structure that supports easy extension and modification without affecting existing functionality.

#### 3.2.5 Usability (for IT Administrators):

*   ********* Easy Configuration:** Configuration should be straightforward, well-documented, and support configuration as code principles.
*   ********* Logging:** Logs should be easily accessible, understandable, and actionable for troubleshooting.
*   ********* Deployment:** Deployment process should be automated, repeatable, and manageable for IT administrators.

## 4. Technical Requirements

*   **Programming Languages:** C# (.NET Framework 4.8 for Outlook Add-in, .NET 8 for Core Logic and Infrastructure).
*   **.NET Framework/Platform:** .NET Framework 4.8, .NET 8.
*   **Database:** SQL Server (initially).
*   **Logging Library:** Serilog or NLog.

### 4.1. VSTO Development Critical Notes

**⚠️ EXTREMELY PERHABITED TO EDIT VSTO - REQUIRES SPECIAL CARE IN WORKING:**

*   **VSTO Add-in Fragility:** VSTO add-ins are extremely sensitive to changes and require special handling during development and deployment.
*   **Registry Dependencies:** VSTO add-ins heavily depend on Windows Registry entries for proper loading and functionality.
*   **Outlook Process Integration:** Add-ins run within the Outlook process, making debugging and error handling complex.
*   **Version Compatibility:** Strict version compatibility requirements between VSTO, .NET Framework, and Office versions.
*   **Deployment Complexity:** VSTO deployment requires careful consideration of user permissions, registry access, and Office security settings.
*   **Debugging Challenges:** Traditional debugging methods may not work reliably with VSTO add-ins.
*   **Security Restrictions:** Office applications have strict security policies that can block VSTO add-ins.
*   **Performance Impact:** Poorly designed VSTO add-ins can significantly impact Outlook performance.

**Development Best Practices:**
*   **Minimal VSTO Code:** Keep VSTO add-in code minimal - only handle Outlook events and communication
*   **Error Isolation:** Implement comprehensive error handling to prevent Outlook crashes
*   **Testing Strategy:** Test extensively in different Office versions and configurations
*   **Deployment Planning:** Plan deployment carefully with proper user permissions and registry access
*   **Monitoring:** Implement robust logging to track add-in behavior and issues
*   **Communication Mechanism (VSTO Add-in to Windows Service):** Named Pipes for same-machine inter-process communication:
    *   Fast and efficient for same-machine communication
    *   Built-in support in .NET Framework and .NET Core
    *   Supports duplex communication for real-time processing
    *   No network overhead or firewall considerations
    *   Automatic connection management and error handling
*   **Architecture:** Clean Architecture principles with SOLID design principles:
    *   **Single Responsibility Principle (SRP):** Each component has one clear purpose
    *   **Open/Closed Principle (OCP):** Open for extension, closed for modification
    *   **Liskov Substitution Principle (LSP):** Subtypes are substitutable for their base types
    *   **Interface Segregation Principle (ISP):** Clients depend only on interfaces they use
    *   **Dependency Inversion Principle (DIP):** High-level modules don't depend on low-level modules
*   **Design Patterns:** Factory Method, Strategy, Repository, Singleton (use judiciously, prefer Dependency Injection), Decorator (for Logger), Retry Pattern.
*   **DRY Principle:** Reusable components eliminate duplication
*   **Separation of Concerns:** Clear boundaries between UI, logic, and data layers
*   **Development Environment:** Visual Studio (latest recommended version).
*   **Source Control:** Git with branching strategies for feature development and release management.
*   **Deployment Environment:** Same-machine deployment where both VSTO add-in and Windows Service run on the user's machine. The .NET 8 Core application is deployed as a Windows Service for reliability and automatic startup.

### 4.2. CLI Commands for Component Creation

**Use CLI commands to create components for consistent project structure:**

```bash
# Create solution and projects
dotnet new sln -n EmailAttachmentProcessor
dotnet new classlib -n EmailProcessor.Domain -f net8.0
dotnet new classlib -n EmailProcessor.Infrastructure -f net8.0
dotnet new classlib -n EmailProcessor.Service -f net8.0
dotnet new classlib -n EmailProcessor.Shared -f net8.0

# Add projects to solution
dotnet sln add src/EmailProcessor.Domain/EmailProcessor.Domain.csproj
dotnet sln add src/EmailProcessor.Infrastructure/EmailProcessor.Infrastructure.csproj
dotnet sln add src/EmailProcessor.Service/EmailProcessor.Service.csproj
dotnet sln add src/EmailProcessor.Shared/EmailProcessor.Shared.csproj

# Create test projects
dotnet new xunit -n EmailProcessor.Domain.Tests -f net8.0
dotnet new xunit -n EmailProcessor.Infrastructure.Tests -f net8.0
dotnet new xunit -n EmailProcessor.Service.Tests -f net8.0

# Add test projects to solution
dotnet sln add tests/EmailProcessor.Domain.Tests/EmailProcessor.Domain.Tests.csproj
dotnet sln add tests/EmailProcessor.Infrastructure.Tests/EmailProcessor.Infrastructure.Tests.csproj
dotnet sln add tests/EmailProcessor.Service.Tests/EmailProcessor.Service.Tests.csproj

# Add project references
dotnet add src/EmailProcessor.Infrastructure/EmailProcessor.Infrastructure.csproj reference src/EmailProcessor.Domain/EmailProcessor.Domain.csproj
dotnet add src/EmailProcessor.Service/EmailProcessor.Service.csproj reference src/EmailProcessor.Domain/EmailProcessor.Domain.csproj
dotnet add src/EmailProcessor.Service/EmailProcessor.Service.csproj reference src/EmailProcessor.Infrastructure/EmailProcessor.Infrastructure.csproj
dotnet add src/EmailProcessor.Service/EmailProcessor.Service.csproj reference src/EmailProcessor.Shared/EmailProcessor.Shared.csproj

# Add NuGet packages
dotnet add src/EmailProcessor.Infrastructure/EmailProcessor.Infrastructure.csproj package Microsoft.EntityFrameworkCore.SqlServer
dotnet add src/EmailProcessor.Infrastructure/EmailProcessor.Infrastructure.csproj package Microsoft.EntityFrameworkCore.Tools
dotnet add src/EmailProcessor.Service/EmailProcessor.Service.csproj package Microsoft.Extensions.Hosting
dotnet add src/EmailProcessor.Service/EmailProcessor.Service.csproj package Microsoft.Extensions.DependencyInjection
dotnet add src/EmailProcessor.Service/EmailProcessor.Service.csproj package Serilog
dotnet add src/EmailProcessor.Service/EmailProcessor.Service.csproj package Serilog.Extensions.Hosting

# Add test packages
dotnet add tests/EmailProcessor.Domain.Tests/EmailProcessor.Domain.Tests.csproj package Moq
dotnet add tests/EmailProcessor.Domain.Tests/EmailProcessor.Domain.Tests.csproj package FluentAssertions
dotnet add tests/EmailProcessor.Infrastructure.Tests/EmailProcessor.Infrastructure.Tests.csproj package Microsoft.EntityFrameworkCore.InMemory
dotnet add tests/EmailProcessor.Infrastructure.Tests/EmailProcessor.Infrastructure.Tests.csproj package Moq
dotnet add tests/EmailProcessor.Infrastructure.Tests/EmailProcessor.Infrastructure.Tests.csproj package FluentAssertions
dotnet add tests/EmailProcessor.Service.Tests/EmailProcessor.Service.Tests.csproj package Moq
dotnet add tests/EmailProcessor.Service.Tests/EmailProcessor.Service.Tests.csproj package FluentAssertions
```

**VSTO Add-in Project Creation (Visual Studio Required):**
```bash
# VSTO projects must be created through Visual Studio
# Use Visual Studio Installer to ensure VSTO tools are installed
# Create VSTO Outlook Add-in project manually in Visual Studio
# Project type: "Outlook VSTO Add-in" (.NET Framework 4.8)
```

## 5. Technical Specifications
### 5.1. System Architecture

The system follows Clean Architecture with SOLID principles, featuring a layered architecture with same-machine deployment where both the VSTO add-in and Windows Service run on the user's machine:

#### 5.1.1 Clean Architecture Layers (Following SOLID Principles):

**1. Presentation Layer (VSTO Add-in):**
- **Single Responsibility:** Only handles Outlook integration and user interface
- **Interface Segregation:** Implements only necessary Outlook interfaces
- **Dependency Inversion:** Depends on abstractions, not concrete implementations
- Components:
  - Outlook Add-in (VSTO) - Runs in Outlook process
  - Named Pipe Client for communication with Windows Service

**2. Application Layer (Windows Service):**
- **Single Responsibility:** Orchestrates business operations without business logic
- **Open/Closed:** Extensible for new use cases without modification
- **Dependency Inversion:** Uses interfaces for all dependencies
- Components:
  - Email Processing Service (Orchestrates email and attachment processing)
  - Attachment Handler Service (Manages attachment storage operations)
  - Database Service (Encapsulates database interactions via Repository Pattern)
  - Logging Service (Handles logging operations)
  - Named Pipe Server for communication with VSTO Add-in

**3. Domain Layer (Core Business Logic):**
- **Single Responsibility:** Contains only business rules and entities
- **Liskov Substitution:** All implementations are substitutable
- **Interface Segregation:** Clean, focused interfaces
- Components:
  - Core Business Entities (Email, Attachment, Document, Metadata)
  - Domain Services (Pure business logic and domain rules)
  - Value Objects (Immutable business concepts)
  - Domain Events (Business event notifications)

**4. Infrastructure Layer (External Concerns):**
- **Single Responsibility:** Handles external system interactions
- **Dependency Inversion:** Implements domain interfaces
- **Open/Closed:** Extensible for new infrastructure concerns
- Components:
  - Database (SQL Server - local or network)
  - File Storage (Local AND UNC - dual storage)
  - Logging Infrastructure
  - Communication Infrastructure (Named Pipes for inter-process communication)

Key Data Flows:

1. Outlook triggers NewMailEx/ItemSend events
2. VSTO Add-in captures email metadata and attachments
3. Data sent to Windows Service via Named Pipes for inter-process communication
4. Email Processing Service orchestrates attachment processing, storage, and metadata persistence
5. Attachment Handler Service manages dual attachment storage operations (local AND UNC)
6. Database Service handles database interactions
7. Logging Service collects logs and writes to configured destinations

**Same-Machine Deployment Benefits:**
- No network latency for communication between add-in and service
- Simplified security model (no network authentication required)
- Faster data transfer for large attachments
- Reduced infrastructure complexity
- Easier troubleshooting and debugging

#### 5.1.2 SOLID Principles Implementation:

**Single Responsibility Principle (SRP):**
- Each service has one reason to change
- Email Processing Service: Only orchestrates email processing
- Attachment Handler Service: Only manages dual file storage operations (local AND UNC)
- Database Service: Only handles data persistence
- Logging Service: Only manages logging operations

**Open/Closed Principle (OCP):**
- New storage providers can be added without modifying existing code
- New email processing rules can be added through configuration
- New logging destinations can be added through dependency injection
- New communication protocols can be added by implementing interfaces

**Liskov Substitution Principle (LSP):**
- All storage implementations are substitutable
- All logging implementations are substitutable
- All database implementations are substitutable
- All communication implementations are substitutable

**Interface Segregation Principle (ISP):**
- Clients depend only on interfaces they actually use
- Storage interfaces are separate from logging interfaces
- Database interfaces are separate from communication interfaces
- Each interface has a focused, cohesive purpose

**Dependency Inversion Principle (DIP):**
- High-level modules (Application Layer) don't depend on low-level modules (Infrastructure)
- Both depend on abstractions (interfaces)
- Domain layer is independent of infrastructure concerns
- Dependencies are injected, not hard-coded

### 5.2. Performance Requirements

*   Process attachments with low latency, aiming for **< 2 seconds for typical emails and attachments** from email receipt to database insertion.
*   Support **concurrent processing** and handle processing of multiple emails simultaneously.
*   Handle large attachments, supporting attachments **up to 100MB in size (configurable)**.
*   Maintain **sub-second database response times** for all database queries under load.

### 5.3. Data Storage Requirements

*   Storage for attachments, supporting **gigabytes of attachment data** in both local AND network storage simultaneously.
*   Database storage for metadata: **Database storage** to accommodate metadata for thousands of emails and attachments (can be local SQL Server or network database).
*   Log storage: **Log file storage** for system logs with configurable retention.

### 5.4. Database Schema Design

#### 5.4.1 Database Overview
The system uses SQL Server to store email and attachment metadata, following normalized design principles with proper indexing for optimal query performance.

#### 5.4.2 Core Tables

**1. Emails Table**
```sql
CREATE TABLE Emails (
    EmailId BIGINT IDENTITY(1,1) PRIMARY KEY,
    Subject NVARCHAR(500) NOT NULL,
    SenderName NVARCHAR(255),
    SenderEmail NVARCHAR(255) NOT NULL,
    RecipientTo NVARCHAR(MAX), -- JSON array for multiple recipients
    RecipientCC NVARCHAR(MAX), -- JSON array for CC recipients
    EmailType TINYINT NOT NULL, -- 1=Received, 2=Sent
    Timestamp DATETIME2 NOT NULL,
    OutlookMessageId NVARCHAR(255), -- Outlook's unique message ID
    ProcessingStatus TINYINT NOT NULL DEFAULT 1, -- 1=Pending, 2=Processing, 3=Completed, 4=Failed
    CreatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    ModifiedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    INDEX IX_Emails_Timestamp (Timestamp),
    INDEX IX_Emails_EmailType (EmailType),
    INDEX IX_Emails_ProcessingStatus (ProcessingStatus),
    INDEX IX_Emails_OutlookMessageId (OutlookMessageId)
);
```

**2. Attachments Table**
```sql
CREATE TABLE Attachments (
    AttachmentId BIGINT IDENTITY(1,1) PRIMARY KEY,
    EmailId BIGINT NOT NULL,
    FileName NVARCHAR(500) NOT NULL,
    OriginalFileName NVARCHAR(500) NOT NULL,
    ContentType NVARCHAR(255),
    FileExtension NVARCHAR(50),
    FileSize BIGINT NOT NULL, -- Size in bytes
    LocalStoragePath NVARCHAR(1000) NOT NULL, -- Full path where file is saved locally
    UncStoragePath NVARCHAR(1000) NOT NULL, -- Full path where file is saved on UNC share
    LocalProcessingStatus TINYINT NOT NULL DEFAULT 1, -- 1=Pending, 2=Processing, 3=Completed, 4=Failed
    UncProcessingStatus TINYINT NOT NULL DEFAULT 1, -- 1=Pending, 2=Processing, 3=Completed, 4=Failed
    LocalErrorMessage NVARCHAR(MAX), -- Error details if local storage failed
    UncErrorMessage NVARCHAR(MAX), -- Error details if UNC storage failed
    CreatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    ModifiedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    FOREIGN KEY (EmailId) REFERENCES Emails(EmailId) ON DELETE CASCADE,
    INDEX IX_Attachments_EmailId (EmailId),
    INDEX IX_Attachments_LocalProcessingStatus (LocalProcessingStatus),
    INDEX IX_Attachments_UncProcessingStatus (UncProcessingStatus),
    INDEX IX_Attachments_FileExtension (FileExtension)
);
```

**3. ProcessingLogs Table**
```sql
CREATE TABLE ProcessingLogs (
    LogId BIGINT IDENTITY(1,1) PRIMARY KEY,
    EmailId BIGINT NULL, -- NULL for system-level logs
    AttachmentId BIGINT NULL, -- NULL for email-level logs
    LogLevel TINYINT NOT NULL, -- 1=Debug, 2=Info, 3=Warning, 4=Error, 5=Fatal
    Message NVARCHAR(MAX) NOT NULL,
    ExceptionDetails NVARCHAR(MAX), -- Stack trace and exception info
    SourceComponent NVARCHAR(100) NOT NULL, -- Component that generated the log
    CorrelationId UNIQUEIDENTIFIER, -- For tracing related log entries
    CreatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    FOREIGN KEY (EmailId) REFERENCES Emails(EmailId) ON DELETE CASCADE,
    FOREIGN KEY (AttachmentId) REFERENCES Attachments(AttachmentId) ON DELETE CASCADE,
    INDEX IX_ProcessingLogs_EmailId (EmailId),
    INDEX IX_ProcessingLogs_AttachmentId (AttachmentId),
    INDEX IX_ProcessingLogs_LogLevel (LogLevel),
    INDEX IX_ProcessingLogs_CreatedDate (CreatedDate),
    INDEX IX_ProcessingLogs_CorrelationId (CorrelationId)
);
```

**4. Configuration Table**
```sql
CREATE TABLE Configuration (
    ConfigId INT IDENTITY(1,1) PRIMARY KEY,
    ConfigKey NVARCHAR(100) NOT NULL UNIQUE,
    ConfigValue NVARCHAR(MAX) NOT NULL,
    Description NVARCHAR(500),
    IsEncrypted BIT NOT NULL DEFAULT 0, -- For sensitive configuration values
    CreatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    ModifiedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    INDEX IX_Configuration_ConfigKey (ConfigKey)
);
```

#### 5.4.3 Database Relationships

**Entity Relationship Diagram:**
```
Emails (1) -----> (Many) Attachments
    |
    +----> (Many) ProcessingLogs

Attachments (1) -----> (Many) ProcessingLogs

Configuration (Standalone table)
```

**Key Relationships:**
- **One-to-Many**: Email → Attachments (One email can have multiple attachments)
- **One-to-Many**: Email → ProcessingLogs (One email can have multiple log entries)
- **One-to-Many**: Attachment → ProcessingLogs (One attachment can have multiple log entries)
- **Cascade Delete**: When an email is deleted, all related attachments and logs are automatically deleted

#### 5.4.4 Indexing Strategy

**Primary Indexes:**
- All tables have clustered indexes on their primary keys
- EmailId, AttachmentId, LogId, ConfigId are auto-incrementing identity columns

**Secondary Indexes:**
- **Performance Indexes**: Timestamp, EmailType, ProcessingStatus for fast filtering
- **Lookup Indexes**: OutlookMessageId for duplicate detection
- **Search Indexes**: FileExtension for attachment queries
- **Storage Status Indexes**: LocalProcessingStatus, UncProcessingStatus for storage monitoring
- **Audit Indexes**: CreatedDate, LogLevel for log analysis

#### 5.4.5 Data Types and Constraints

**String Fields:**
- NVARCHAR for Unicode support (emails may contain international characters)
- Appropriate lengths based on expected data sizes
- JSON arrays for multiple recipients (To, CC fields)

**Numeric Fields:**
- BIGINT for IDs and file sizes (supports large files)
- TINYINT for status and type fields (efficient storage)

**Date Fields:**
- DATETIME2 for high precision timestamps
- UTC timestamps for consistency across time zones

**Constraints:**
- NOT NULL constraints on required fields
- Foreign key constraints with CASCADE DELETE
- Unique constraints on OutlookMessageId and ConfigKey

#### 5.4.6 Sample Data

**Configuration Table Sample:**
```sql
INSERT INTO Configuration (ConfigKey, ConfigValue, Description) VALUES
('Storage.LocalBasePath', 'C:\EmailAttachments', 'Base directory for local attachment storage'),
('Storage.UncBasePath', '\\server\share\EmailAttachments', 'Base UNC path for network attachment storage'),
('Storage.MaxFileSize', '104857600', 'Maximum file size in bytes (100MB)'),
('Processing.RetryCount', '3', 'Number of retry attempts for failed processing'),
('Logging.Level', 'Information', 'Minimum log level to record');
```

**Emails Table Sample:**
```sql
INSERT INTO Emails (Subject, SenderName, SenderEmail, EmailType, Timestamp) VALUES
('Project Update', 'John Doe', '<EMAIL>', 1, '2025-01-27T10:30:00Z'),
('Meeting Minutes', 'Jane Smith', '<EMAIL>', 2, '2025-01-27T14:15:00Z');
```

#### 5.4.7 Database Maintenance

**Regular Maintenance Tasks:**
- **Index Rebuilding**: Weekly index maintenance for optimal performance
- **Statistics Updates**: Regular statistics updates for query optimization
- **Log Archiving**: Monthly archiving of old ProcessingLogs entries
- **Data Cleanup**: Periodic cleanup of failed processing records

**Backup Strategy:**
- **Full Backup**: Daily full database backup
- **Transaction Log Backup**: Every 15 minutes during business hours
- **Point-in-Time Recovery**: Support for point-in-time recovery within 24 hours

### 5.5. Same-Machine Deployment Architecture

**VSTO Add-in (Client Side):**
- Runs within the Outlook process
- Monitors Outlook events (NewMailEx, ItemSend)
- Extracts email metadata and attachment data
- Communicates with Windows Service via Named Pipes
- Handles communication errors and retries

**Windows Service (Server Side):**
- Runs as a background Windows Service
- Listens for Named Pipe connections from VSTO add-in
- Processes email attachments and saves to BOTH local AND UNC storage
- Manages database operations
- Handles logging and error reporting
- Automatically starts with Windows and restarts on failure

**Communication Protocol:**
- Named Pipes for fast, reliable same-machine communication
- Structured message format for email metadata and attachment data
- Automatic connection management and error handling
- Support for large attachment data transfer

**Dual Storage Processing:**
- Each attachment is saved to both local storage AND UNC storage
- Independent status tracking for each storage location
- Parallel processing for improved performance
- Failover capability - if one storage fails, the other continues

### 5.6. Scalable Folder Structure (Following DRY and Separation of Concerns)

```
EmailAttachmentProcessor/
├── src/
│   ├── VSTO.Addin/                    # Presentation Layer (.NET Framework 4.8)
│   │   ├── Components/
│   │   │   ├── OutlookIntegration/    # Single Responsibility: Outlook-specific code
│   │   │   ├── Communication/         # Single Responsibility: Named Pipe client
│   │   │   └── Configuration/         # Single Responsibility: Add-in configuration
│   │   ├── Interfaces/                # Interface Segregation: Only needed interfaces
│   │   └── Models/                    # Data transfer objects
│   │
│   ├── EmailProcessor.Service/        # Application Layer (.NET 8)
│   │   ├── Services/                  # Single Responsibility: Each service has one purpose
│   │   │   ├── EmailProcessingService/
│   │   │   ├── AttachmentHandlerService/
│   │   │   ├── DatabaseService/
│   │   │   └── LoggingService/
│   │   ├── Communication/             # Named Pipe server implementation
│   │   ├── Configuration/             # Service configuration
│   │   └── DependencyInjection/       # DI container setup
│   │
│   ├── EmailProcessor.Domain/         # Domain Layer (.NET 8)
│   │   ├── Entities/                  # Core business entities
│   │   │   ├── Email.cs
│   │   │   ├── Attachment.cs
│   │   │   └── Document.cs
│   │   ├── Services/                  # Pure business logic
│   │   ├── Interfaces/                # Domain interfaces
│   │   ├── ValueObjects/              # Immutable business concepts
│   │   └── Events/                    # Domain events
│   │
│   ├── EmailProcessor.Infrastructure/ # Infrastructure Layer (.NET 8)
│   │   ├── Database/                  # Single Responsibility: Database concerns
│   │   │   ├── Repositories/          # Repository implementations
│   │   │   ├── Context/               # Database context
│   │   │   └── Migrations/            # Database migrations
│   │   ├── Storage/                   # Single Responsibility: Dual file storage concerns
│   │   │   ├── LocalStorage/
│   │   │   ├── UncStorage/
│   │   │   └── DualStorageService/    # Orchestrates both storage operations
│   │   ├── Logging/                   # Single Responsibility: Logging concerns
│   │   └── Communication/             # Single Responsibility: Communication concerns
│   │
│   └── EmailProcessor.Shared/         # Shared components (DRY principle)
│       ├── Models/                    # Shared data models
│       ├── Interfaces/                # Shared interfaces
│       ├── Constants/                 # Shared constants
│       └── Utilities/                 # Shared utilities
│
├── tests/                             # Test projects following same structure
│   ├── EmailProcessor.Domain.Tests/
│   ├── EmailProcessor.Infrastructure.Tests/
│   └── EmailProcessor.Service.Tests/
│
├── docs/                              # Documentation
├── scripts/                           # Build and deployment scripts
└── config/                            # Configuration files
```

**Key Architectural Benefits:**
- **DRY Principle:** Shared components eliminate duplication
- **Separation of Concerns:** Clear boundaries between layers
- **Single Responsibility:** Each folder has one clear purpose
- **Scalability:** Easy to add new features without affecting existing code
- **Maintainability:** Clear structure makes code easy to find and modify
- **Testability:** Each layer can be tested independently

## 6. Error Handling and Recovery
### 6.1. Error Scenarios

*   Attachment too large (exceeds configurable size limit)
*   Storage full (local storage or UNC share space exceeded)
*   Database connection failure (database unavailable, network issues, authentication failures)
*   Invalid file types (attachments with unsupported file types)
*   Network connectivity issues (network outages, timeouts)
*   Data corruption during processing or storage operations
*   Authentication and authorization failures (access denied to storage or database)
*   Configuration errors (invalid configuration parameters, missing configuration settings)
*   Application exceptions and runtime errors
*   **Named Pipe communication failures** (Windows Service not running, connection timeouts)
*   **Windows Service failures** (service crashes, startup failures)
*   **VSTO Add-in failures** (Outlook process issues, add-in loading failures)
*   **VSTO Registry Issues** (corrupted registry entries, missing VSTO runtime)
*   **Office Security Blocking** (Office security policies blocking add-in execution)
*   **VSTO Runtime Version Mismatch** (incompatible VSTO runtime versions)
*   **Outlook Process Crashes** (VSTO add-in causing Outlook to crash)

### 6.2. Recovery Mechanisms

*   **Retry Mechanism:** Implement a retry mechanism with configurable exponential backoff for transient errors.
*   **Named Pipe Connection Recovery:** Automatic reconnection to Windows Service if communication is lost.
*   **Windows Service Auto-Restart:** Service configured to automatically restart on failure.
*   **VSTO Add-in Resilience:** Graceful handling of Outlook process restarts and add-in reloading.
*   **VSTO Registry Recovery:** Automatic detection and repair of corrupted VSTO registry entries.
*   **Office Security Bypass:** Implementation of proper signing and security configurations for VSTO add-ins.
*   **VSTO Runtime Management:** Automatic detection and installation of required VSTO runtime components.
*   **Outlook Crash Prevention:** Comprehensive error handling to prevent VSTO add-in from crashing Outlook.
*   **Detailed Error Logging:** Provide detailed error logs with context information to facilitate troubleshooting.
*   **Manual Intervention Procedures:** Document clear procedures for manual intervention and recovery in case of critical failures.

## 7. Security Considerations
### 7.1. Data Security

*   **Encryption in Transit:** Enforce encryption in transit for all data communication between system components, including HTTPS/TLS for API communication and encrypted database connections.
*   **Secure Configuration Management:** Securely manage configuration data, including database connection strings and API keys.
*   **Secure Log Storage:** Store logs securely, protecting sensitive information in logs.

### 7.2. Authentication and Authorization

*   **Access Control:** Implement access control to system resources and operations based on user roles and privileges.
*   **Least Privilege Principle:** Apply the principle of least privilege, granting users and services only the minimum necessary permissions.
*   **Audit Logging:** Log authentication and authorization events for security auditing.

## 8. Testing and Validation
### 8.1. Unit Testing

*   **Code Coverage:** Aim for **> 80% code coverage** for unit tests, focusing on testing core logic and individual components following SOLID principles.
*   **Mocking and Stubbing:** Utilize mocking and stubbing frameworks to isolate unit tests from external dependencies, ensuring each component can be tested independently.
*   **SOLID Testing:** Test each component according to its single responsibility, ensuring interfaces are properly segregated and dependencies are inverted.
*   **DRY Testing:** Create reusable test utilities and fixtures to eliminate test code duplication.
*   **Automated Unit Test Execution:** Implement automated unit test execution as part of the CI/CD pipeline.

### 8.2. Integration Testing

*   **End-to-End Integration Tests:** Develop end-to-end integration tests to verify the integration and data flow between different system components while respecting layer boundaries.
*   **Component Integration Tests:** Implement component integration tests to test the interaction between related components, ensuring SOLID principles are maintained.
*   **Layer Integration Tests:** Test integration between Clean Architecture layers (Presentation, Application, Domain, Infrastructure) to ensure proper separation of concerns.
*   **Interface Integration Tests:** Verify that all interfaces are properly implemented and substitutable according to Liskov Substitution Principle.
*   **Performance Testing:** Conduct performance testing under realistic load conditions to evaluate system performance.

### 8.3. User Acceptance Testing (UAT)

*   **Real-World Scenario Testing:** Conduct User Acceptance Testing (UAT) in a production-like environment with real-world scenarios.
*   **User Feedback Collection:** Gather feedback from pilot users during UAT to identify usability issues and areas for improvement.
*   **Bug Tracking and Resolution:** Implement a bug tracking system to track and resolve bugs identified during UAT.

## 9. Deployment and Maintenance
### 9.1. Deployment Strategy

*   **Automated Deployment Pipeline (CI/CD):** Implement an automated CI/CD pipeline for building, testing, and deploying the Email Attachment Processor.
*   **Rollback Plan:** Develop a rollback plan to quickly revert to the previous version in case of deployment failures.
*   **Deployment Documentation:** Create deployment documentation to standardize and document the deployment process.

### 9.2. Monitoring and Maintenance

*   **Basic Logging and Monitoring:** Implement basic logging and monitoring to track system health and performance.
*   **Regular Maintenance:** Establish a process for regular maintenance, including security updates and system health checks.
*   **Troubleshooting Documentation:** Create troubleshooting guides to document common issues and resolution steps.

## 10. Future Considerations

### 10.1. Potential Enhancements

*   **Cloud Storage Integration:** Consider integration with cloud storage providers for enhanced scalability (following Open/Closed Principle for extensibility).
*   **Web-based Management Interface:** Develop a web-based management interface for configuration and monitoring (maintaining separation of concerns).
*   **Advanced File Processing:** Consider adding support for additional file types and processing capabilities (extending existing interfaces without modification).
*   **API for Integration:** Develop APIs to facilitate integration with other enterprise systems (following Interface Segregation Principle).
*   **Additional Storage Providers:** Extend storage capabilities through new implementations of existing interfaces (Liskov Substitution Principle).
*   **Enhanced Logging Destinations:** Add new logging providers without modifying existing code (Open/Closed Principle).

### 10.2. Implementation Timeline

*   **Phase 1:** Core functionality implementation (Q1 2025)
*   **Phase 2:** Enhanced error handling and performance optimization (Q2 2025)
*   **Phase 3:** Additional features and integrations (Q3-Q4 2025)

## 11. Appendices
### 11.1. Glossary of Terms

*   VSTO: Visual Studio Tools for Office
*   gRPC: Google Remote Procedure Call framework
*   UNC: Universal Naming Convention for network paths
*   ORM: Object-Relational Mapping
*   DI: Dependency Injection
*   CI/CD: Continuous Integration/Continuous Deployment
*   **SOLID:** Software design principles (SRP, OCP, LSP, ISP, DIP)
*   **SRP:** Single Responsibility Principle - Each class has one reason to change
*   **OCP:** Open/Closed Principle - Open for extension, closed for modification
*   **LSP:** Liskov Substitution Principle - Subtypes are substitutable for base types
*   **ISP:** Interface Segregation Principle - Clients depend only on interfaces they use
*   **DIP:** Dependency Inversion Principle - High-level modules don't depend on low-level modules
*   **DRY:** Don't Repeat Yourself - Eliminate code duplication
*   **Clean Architecture:** Layered architecture with dependency inversion

### 11.2. References

1. Microsoft Outlook VSTO Documentation
2. .NET Framework 4.8 API Reference
3. .NET 8 Core Documentation
4. Named Pipes for .NET Implementation Guide
5. SQL Server Best Practices
6. Clean Architecture Principles (Robert C. Martin)
7. SOLID Principles (Robert C. Martin)
8. Serilog or NLog Documentation
9. Dependency Injection in .NET
10. Repository Pattern Implementation Guide

### 11.3. Revision History

*   1.0 - Initial PRD (2025-01-27)
*   1.1 - Enhanced PRD with detailed feature breakdown (2025-01-27)
*   1.2 - Simplified PRD focusing on core functionality (2025-07-31)

### 11.4. VSTO Development Challenges and Best Practices

**⚠️ CRITICAL VSTO DEVELOPMENT CONSIDERATIONS:**

#### 11.4.1 VSTO Development Challenges

**Technical Challenges:**
*   **Process Integration:** VSTO add-ins run within the Outlook process, making them vulnerable to Outlook crashes
*   **Memory Management:** Poor memory management can cause Outlook to become unstable
*   **Threading Issues:** VSTO add-ins must handle threading carefully to avoid blocking the UI thread
*   **Version Compatibility:** Strict compatibility requirements between VSTO, .NET Framework, and Office versions
*   **Registry Dependencies:** Heavy reliance on Windows Registry for configuration and loading
*   **Security Restrictions:** Office applications have strict security policies that can block add-ins

**Development Challenges:**
*   **Debugging Complexity:** Traditional debugging methods may not work reliably
*   **Testing Difficulties:** Testing VSTO add-ins requires specific environments and configurations
*   **Deployment Complexity:** Requires careful consideration of user permissions and registry access
*   **Error Isolation:** Errors in VSTO add-ins can crash the entire Outlook application
*   **Performance Impact:** Poorly designed add-ins can significantly impact Outlook performance

#### 11.4.2 VSTO Development Best Practices

**Code Organization:**
*   **Minimal VSTO Code:** Keep VSTO add-in code minimal - only handle Outlook events and communication
*   **Separation of Concerns:** Move business logic to the Windows Service, keep VSTO focused on Outlook integration
*   **Error Isolation:** Implement comprehensive try-catch blocks around all VSTO operations
*   **Resource Management:** Properly dispose of COM objects and resources

**Error Handling:**
*   **Graceful Degradation:** Ensure add-in continues to function even if some features fail
*   **Comprehensive Logging:** Log all operations and errors for troubleshooting
*   **User Feedback:** Provide clear feedback to users when operations fail
*   **Recovery Mechanisms:** Implement automatic recovery for common failure scenarios

**Testing Strategy:**
*   **Multiple Office Versions:** Test on different Office versions (2016, 2019, 365)
*   **Different Configurations:** Test with various Outlook configurations and security settings
*   **Automated Testing:** Implement automated tests for VSTO functionality
*   **Manual Testing:** Extensive manual testing in real-world scenarios

**Deployment Considerations:**
*   **User Permissions:** Ensure users have necessary permissions for registry access
*   **Security Signing:** Properly sign VSTO add-ins to avoid security warnings
*   **Installation Scripts:** Create reliable installation and uninstallation scripts
*   **Rollback Plan:** Have a plan to quickly remove problematic add-ins

#### 11.4.3 VSTO Troubleshooting Guide

**Common Issues and Solutions:**

**Add-in Not Loading:**
*   Check registry entries for VSTO add-in
*   Verify .NET Framework version compatibility
*   Check Office security settings
*   Ensure VSTO runtime is installed

**Outlook Crashes:**
*   Review VSTO add-in logs for errors
*   Check for memory leaks in add-in code
*   Verify proper COM object disposal
*   Test add-in in safe mode

**Performance Issues:**
*   Monitor add-in memory usage
*   Check for blocking operations on UI thread
*   Optimize event handling code
*   Implement proper async/await patterns

**Communication Failures:**
*   Verify Windows Service is running
*   Check Named Pipe permissions
*   Test communication independently
*   Implement retry mechanisms

#### 11.4.4 VSTO Development Tools

**Required Tools:**
*   **Visual Studio:** Latest version with VSTO tools installed
*   **Office Developer Tools:** Ensure VSTO templates are available
*   **VSTO Runtime:** Required runtime components for deployment
*   **Registry Editor:** For troubleshooting registry issues

**Recommended Tools:**
*   **Process Monitor:** For monitoring registry and file system access
*   **DebugDiag:** For analyzing crashes and memory issues
*   **VSTO Log Viewer:** For viewing VSTO-specific logs
*   **Office Configuration Analyzer:** For analyzing Office configuration issues

--- END OF FILE Email_Attachment_Processor_PRD_v1.2.txt ---