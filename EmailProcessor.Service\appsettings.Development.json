{"EmailProcessor": {"Storage": {"LocalBasePath": "C:\\Temp\\EmailAttachments", "UncBasePath": "C:\\Temp\\UncStorage", "MaxFileSize": 104857600, "CreateDirectories": true, "DirectoryStructure": "{Year}\\{Month}\\{Day}\\{EmailType}", "UncUsername": "", "UncPassword": ""}, "Processing": {"RetryCount": 2, "RetryDelaySeconds": 2, "MaxConcurrentProcessing": 5, "ProcessingTimeoutSeconds": 15, "ProcessEmailTypes": ["Received", "<PERSON><PERSON>"], "ExcludedFileExtensions": [".exe", ".bat", ".cmd", ".scr"]}, "Database": {"ConnectionString": "Server=(localdb)\\mssqllocaldb;Database=EmailProcessor;Trusted_Connection=true;", "CommandTimeout": 30, "EnableRetryOnFailure": true, "MaxRetryAttempts": 2, "RetryDelaySeconds": 1}, "Logging": {"LogLevel": "Debug", "LogFilePath": "C:\\Temp\\Logs\\EmailProcessor\\EmailProcessor-.log", "LogFileRetentionDays": 7, "EnableConsoleLogging": true, "EnableFileLogging": true, "UseStructuredLogging": false, "MaxLogFileSizeBytes": 10485760, "FlushIntervalSeconds": 1}, "Communication": {"NamedPipeName": "EmailProcessorPipe_Dev", "ConnectionTimeoutSeconds": 5, "MaxMessageSize": 10485760, "RetryCount": 2, "RetryDelaySeconds": 1, "EnableCompression": false}}, "Serilog": {"Using": ["Serilog.Sinks.File", "Serilog.Sinks.Console"], "MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Information", "System": "Information"}}, "WriteTo": [{"Name": "File", "Args": {"path": "C:\\Temp\\Logs\\EmailProcessor\\EmailProcessor-.log", "rollingInterval": "Day", "retainedFileCountLimit": 7, "fileSizeLimitBytes": 10485760, "shared": true, "flushToDiskInterval": "00:00:01", "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] [{SourceContext}] {Message:lj} {CorrelationId} {NewLine}{Exception}"}}, {"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "{Timestamp:HH:mm:ss} [{Level:u3}] {Message:lj} {CorrelationId} {NewLine}{Exception}"}}], "Enrich": ["FromLogContext"], "Properties": {"Application": "EmailProcessor.Service.Dev"}}}