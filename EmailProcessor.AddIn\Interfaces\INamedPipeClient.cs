using System;
using System.Threading.Tasks;
using EmailProcessor.AddIn.Models;
using EmailProcessor.AddIn.Communication;

namespace EmailProcessor.AddIn.Interfaces
{
    /// <summary>
    /// Enhanced interface for Named Pipe client communication
    /// Phase 4: Added connection error event for better error handling
    /// </summary>
    public interface INamedPipeClient : IDisposable
    {
        /// <summary>
        /// Gets whether the client is connected to the server
        /// </summary>
        bool IsConnected { get; }

        /// <summary>
        /// Connects to the Named Pipe server
        /// </summary>
        /// <param name="timeoutSeconds">Connection timeout in seconds</param>
        /// <returns>True if connection successful</returns>
        Task<bool> ConnectAsync(int timeoutSeconds = 10);

        /// <summary>
        /// Disconnects from the Named Pipe server
        /// </summary>
        Task DisconnectAsync();

        /// <summary>
        /// Sends a processing message to the Windows Service
        /// </summary>
        /// <param name="message">The processing message to send</param>
        /// <returns>The response from the service</returns>
        Task<ProcessingResponse> SendMessageAsync(ProcessingMessage message);

        /// <summary>
        /// Tests the connection to the Windows Service
        /// </summary>
        /// <returns>True if connection is working</returns>
        Task<bool> TestConnectionAsync();

        /// <summary>
        /// Event raised when connection is lost
        /// </summary>
        event EventHandler ConnectionLost;

        /// <summary>
        /// Event raised when connection is established
        /// </summary>
        event EventHandler Connected;

        /// <summary>
        /// Event raised when a connection error occurs
        /// Phase 4: Added for better error handling and diagnostics
        /// </summary>
        event EventHandler<ConnectionErrorEventArgs> ConnectionError;
    }
} 