namespace EmailProcessor.Shared.Constants
{
    /// <summary>
    /// Constants for email processing
    /// </summary>
    public static class ProcessingConstants
    {
        /// <summary>
        /// Default Named Pipe name for communication
        /// </summary>
        public const string DefaultNamedPipeName = "EmailProcessorPipe";

        /// <summary>
        /// Default connection timeout in seconds
        /// </summary>
        public const int DefaultConnectionTimeoutSeconds = 10;

        /// <summary>
        /// Default retry count for operations
        /// </summary>
        public const int DefaultRetryCount = 3;

        /// <summary>
        /// Default retry delay in seconds
        /// </summary>
        public const int DefaultRetryDelaySeconds = 2;

        /// <summary>
        /// Maximum file size in bytes (100MB)
        /// </summary>
        public const long MaxFileSizeBytes = 104857600;

        /// <summary>
        /// Maximum message size in bytes (100MB)
        /// </summary>
        public const long MaxMessageSizeBytes = 104857600;

        /// <summary>
        /// Message types for communication
        /// </summary>
        public static class MessageTypes
        {
            public const string EmailProcessingRequest = "EmailProcessingRequest";
            public const string EmailProcessingResponse = "EmailProcessingResponse";
            public const string TestConnection = "TestConnection";
            public const string TestConnectionResponse = "TestConnectionResponse";
        }

        /// <summary>
        /// Email types
        /// </summary>
        public static class EmailTypes
        {
            public const string Received = "Received";
            public const string Sent = "Sent";
        }

        /// <summary>
        /// Processing statuses
        /// </summary>
        public static class ProcessingStatuses
        {
            public const string Pending = "Pending";
            public const string Processing = "Processing";
            public const string Completed = "Completed";
            public const string Failed = "Failed";
            public const string Skipped = "Skipped";
        }

        /// <summary>
        /// Excluded file extensions for security
        /// </summary>
        public static readonly string[] ExcludedFileExtensions = 
        {
            ".exe", ".bat", ".cmd", ".scr", ".pif", ".com", ".vbs", ".js", ".jar", ".msi"
        };

        /// <summary>
        /// Allowed file extensions (if empty, all extensions are allowed except excluded ones)
        /// </summary>
        public static readonly string[] AllowedFileExtensions = 
        {
            ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".txt", ".rtf",
            ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".zip", ".rar", ".7z"
        };
    }
} 