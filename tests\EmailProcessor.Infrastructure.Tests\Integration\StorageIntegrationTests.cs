using System;
using System.IO;
using System.Threading.Tasks;
using EmailProcessor.Domain.Interfaces;
using EmailProcessor.Infrastructure.Logging;
using EmailProcessor.Infrastructure.Storage.DualStorageService;
using EmailProcessor.Infrastructure.Storage.LocalStorage;
using EmailProcessor.Infrastructure.Storage.UncStorage;
using FluentAssertions;
using Xunit;

namespace EmailProcessor.Infrastructure.Tests.Integration
{
    /// <summary>
    /// Integration tests for storage operations that test the complete storage stack
    /// </summary>
    public class StorageIntegrationTests : IDisposable
    {
        private readonly string _testLocalPath;
        private readonly string _testUncPath;
        private readonly ILoggingProvider _loggingProvider;
        private readonly LocalStorageProvider _localStorageProvider;
        private readonly DualStorageService _dualStorageService;

        public StorageIntegrationTests()
        {
            _testLocalPath = Path.Combine(Path.GetTempPath(), "EmailProcessor_IntegrationTests", "Local", Guid.NewGuid().ToString());
            _testUncPath = Path.Combine(Path.GetTempPath(), "EmailProcessor_IntegrationTests", "UNC", Guid.NewGuid().ToString());

            // Create test directories
            Directory.CreateDirectory(_testLocalPath);
            Directory.CreateDirectory(_testUncPath);

            // Create logging provider
            var loggingConfig = new SerilogLoggingConfiguration
            {
                EnableConsoleLogging = false,
                EnableFileLogging = false // Disable file logging for integration tests
            };
            _loggingProvider = new SerilogLoggingProvider(loggingConfig);

            // Create storage providers
            _localStorageProvider = new LocalStorageProvider(_testLocalPath, _loggingProvider);

            // For integration tests, use another LocalStorageProvider as a mock UNC provider
            // In real scenarios, this would be a real UNC path
            var mockUncStorageProvider = new MockUncStorageProvider(_testUncPath, _loggingProvider);

            _dualStorageService = new DualStorageService(
                _localStorageProvider,
                mockUncStorageProvider,
                _loggingProvider);
        }

        public void Dispose()
        {
            _loggingProvider?.Dispose();

            // Clean up test directories
            if (Directory.Exists(_testLocalPath))
            {
                try { Directory.Delete(Path.GetDirectoryName(_testLocalPath), true); }
                catch { /* Ignore cleanup errors */ }
            }
        }

        [Fact]
        public async Task EndToEndStorageTest_SaveFileAndVerifyBothLocations()
        {
            // Arrange
            var fileName = "integration_test.txt";
            var fileContent = "This is an integration test file content with special characters: åäö 中文 🚀";
            var fileData = System.Text.Encoding.UTF8.GetBytes(fileContent);
            var subDirectory = "integration_tests";

            // Act
            var result = await _dualStorageService.SaveFileAsync(fileName, fileData, subDirectory);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccessful.Should().BeTrue();
            result.FileName.Should().Be(fileName);
            result.SubDirectory.Should().Be(subDirectory);

            // Verify local storage
            result.LocalStorageResult.Should().NotBeNull();
            result.LocalStorageResult.IsSuccessful.Should().BeTrue();
            result.LocalStorageResult.StorageType.Should().Be(Domain.Interfaces.StorageType.Local);

            // Verify UNC storage
            result.UncStorageResult.Should().NotBeNull();
            result.UncStorageResult.IsSuccessful.Should().BeTrue();
            result.UncStorageResult.StorageType.Should().Be(Domain.Interfaces.StorageType.Unc);

            // Verify files actually exist and have correct content
            var localFilePath = Path.Combine(_testLocalPath, subDirectory, fileName);
            var uncFilePath = Path.Combine(_testUncPath, subDirectory, fileName);

            File.Exists(localFilePath).Should().BeTrue();
            File.Exists(uncFilePath).Should().BeTrue();

            var localContent = await File.ReadAllTextAsync(localFilePath, System.Text.Encoding.UTF8);
            var uncContent = await File.ReadAllTextAsync(uncFilePath, System.Text.Encoding.UTF8);

            localContent.Should().Be(fileContent);
            uncContent.Should().Be(fileContent);
        }

        [Fact]
        public async Task ConnectivityTest_VerifiesBothStoragesAreAccessible()
        {
            // Act
            var result = await _dualStorageService.TestConnectivityAsync();

            // Assert
            result.Should().BeTrue();
        }

        [Fact]
        public async Task HealthStatusTest_ReturnsValidHealthInformation()
        {
            // Act
            var result = await _dualStorageService.GetStorageHealthStatusAsync();

            // Assert
            result.Should().NotBeNull();
            result.LocalStorageAvailable.Should().BeTrue();
            result.UncStorageAvailable.Should().BeTrue();
            result.LocalAvailableSpace.Should().BeGreaterThan(0);
            result.UncAvailableSpace.Should().BeGreaterThan(0);
            result.Timestamp.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(10));
        }

        [Fact]
        public async Task LargeFileTest_HandlesLargeFilesCorrectly()
        {
            // Arrange
            var fileName = "large_file_test.bin";
            var fileSize = 1024 * 1024; // 1MB
            var fileData = new byte[fileSize];
            new Random().NextBytes(fileData); // Fill with random data

            // Act
            var result = await _dualStorageService.SaveFileAsync(fileName, fileData);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccessful.Should().BeTrue();

            // Verify file sizes
            var localFilePath = Path.Combine(_testLocalPath, fileName);
            var uncFilePath = Path.Combine(_testUncPath, fileName);

            var localFileInfo = new FileInfo(localFilePath);
            var uncFileInfo = new FileInfo(uncFilePath);

            localFileInfo.Length.Should().Be(fileSize);
            uncFileInfo.Length.Should().Be(fileSize);

            // Verify file content integrity
            var localContent = await File.ReadAllBytesAsync(localFilePath);
            var uncContent = await File.ReadAllBytesAsync(uncFilePath);

            localContent.Should().BeEquivalentTo(fileData);
            uncContent.Should().BeEquivalentTo(fileData);
        }

        [Fact]
        public async Task SpecialCharactersInPathTest_HandlesSpecialCharacters()
        {
            // Arrange
            var fileName = "test with spaces & special chars.txt";
            var fileData = System.Text.Encoding.UTF8.GetBytes("Special file content");
            var subDirectory = "special chars & symbols";

            // Act
            var result = await _dualStorageService.SaveFileAsync(fileName, fileData, subDirectory);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccessful.Should().BeTrue();

            // Verify files exist
            var localFilePath = Path.Combine(_testLocalPath, subDirectory, fileName);
            var uncFilePath = Path.Combine(_testUncPath, subDirectory, fileName);

            File.Exists(localFilePath).Should().BeTrue();
            File.Exists(uncFilePath).Should().BeTrue();
        }
    }

    /// <summary>
    /// Mock UNC storage provider that simulates UNC behavior using local file system
    /// This allows integration testing without requiring actual UNC network shares
    /// </summary>
    internal class MockUncStorageProvider : IStorageProvider
    {
        private readonly LocalStorageProvider _underlyingProvider;

        public MockUncStorageProvider(string basePath, ILoggingProvider loggingProvider)
        {
            _underlyingProvider = new LocalStorageProvider(basePath, loggingProvider);
        }

        public Domain.Interfaces.StorageType StorageType => Domain.Interfaces.StorageType.Unc;

        public string BasePath => _underlyingProvider.BasePath;

        public Task<Domain.ValueObjects.FilePath> SaveFileAsync(byte[] fileData, string fileName, string directoryPath)
        {
            return _underlyingProvider.SaveFileAsync(fileData, fileName, directoryPath);
        }

        public Task<Domain.ValueObjects.FilePath> SaveFileAsync(Stream fileStream, string fileName, string directoryPath)
        {
            return _underlyingProvider.SaveFileAsync(fileStream, fileName, directoryPath);
        }

        public Task CreateDirectoryAsync(string directoryPath)
        {
            return _underlyingProvider.CreateDirectoryAsync(directoryPath);
        }

        public Task<bool> FileExistsAsync(string filePath)
        {
            return _underlyingProvider.FileExistsAsync(filePath);
        }

        public Task<bool> DirectoryExistsAsync(string directoryPath)
        {
            return _underlyingProvider.DirectoryExistsAsync(directoryPath);
        }

        public Task DeleteFileAsync(string filePath)
        {
            return _underlyingProvider.DeleteFileAsync(filePath);
        }

        public Task<FileInfo> GetFileInfoAsync(string filePath)
        {
            return _underlyingProvider.GetFileInfoAsync(filePath);
        }

        public Task<long> GetAvailableSpaceAsync()
        {
            return _underlyingProvider.GetAvailableSpaceAsync();
        }

        public Task<bool> TestConnectivityAsync()
        {
            return _underlyingProvider.TestConnectivityAsync();
        }
    }
}