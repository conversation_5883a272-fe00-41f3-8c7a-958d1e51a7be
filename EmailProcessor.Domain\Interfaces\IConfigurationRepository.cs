using System.Collections.Generic;
using System.Threading.Tasks;
using EmailProcessor.Domain.Entities;

namespace EmailProcessor.Domain.Interfaces
{
    public interface IConfigurationRepository
    {
        Task<Configuration> GetByKeyAsync(string configKey);
        Task<IEnumerable<Configuration>> GetAllAsync();
        Task<Configuration> AddAsync(Configuration configuration);
        Task<Configuration> UpdateAsync(Configuration configuration);
        Task<bool> DeleteAsync(string configKey);
        Task<bool> ExistsAsync(string configKey);
        Task<IEnumerable<Configuration>> GetByEncryptedStatusAsync(bool isEncrypted);
    }
} 