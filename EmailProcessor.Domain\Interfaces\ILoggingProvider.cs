using System;
using System.Threading.Tasks;
using EmailProcessor.Domain.ValueObjects;

namespace EmailProcessor.Domain.Interfaces
{
    /// <summary>
    /// Interface for logging providers
    /// </summary>
    public interface ILoggingProvider
    {
        /// <summary>
        /// Logs a debug message
        /// </summary>
        Task LogDebugAsync(string message, string sourceComponent, Guid? correlationId = null);

        /// <summary>
        /// Logs an information message
        /// </summary>
        Task LogInformationAsync(string message, string sourceComponent, Guid? correlationId = null);

        /// <summary>
        /// Logs a warning message
        /// </summary>
        Task LogWarningAsync(string message, string sourceComponent, Guid? correlationId = null);

        /// <summary>
        /// Logs an error message
        /// </summary>
        Task LogErrorAsync(string message, string sourceComponent, Exception exception = null, Guid? correlationId = null);

        /// <summary>
        /// Logs a fatal message
        /// </summary>
        Task LogFatalAsync(string message, string sourceComponent, Exception exception = null, Guid? correlationId = null);

        /// <summary>
        /// Logs a message with the specified log level
        /// </summary>
        Task LogAsync(LogLevel logLevel, string message, string sourceComponent, Exception exception = null, Guid? correlationId = null);

        /// <summary>
        /// Gets the current log level
        /// </summary>
        LogLevel CurrentLogLevel { get; }

        /// <summary>
        /// Sets the log level
        /// </summary>
        void SetLogLevel(LogLevel logLevel);

        /// <summary>
        /// Checks if the specified log level is enabled
        /// </summary>
        bool IsEnabled(LogLevel logLevel);
    }
} 