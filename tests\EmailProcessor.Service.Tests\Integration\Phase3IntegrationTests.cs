using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EmailProcessor.Shared.Models;
using EmailProcessor.Shared.Utilities;
using FluentAssertions;
using Xunit;

namespace EmailProcessor.Service.Tests.Integration
{
    public class Phase3IntegrationTests
    {
        [Fact]
        public void SharedModels_ShouldBeCompatible()
        {
            // Test that shared models can be created and serialized
            var request = new EmailProcessingRequest
            {
                CorrelationId = Guid.NewGuid(),
                Timestamp = DateTime.UtcNow,
                Data = new EmailProcessingData
                {
                    Email = new EmailData
                    {
                        Subject = "Test Email",
                        SenderName = "Test Sender",
                        SenderEmail = "<EMAIL>",
                        RecipientTo = new List<string> { "<EMAIL>" },
                        RecipientCC = new List<string>(),
                        EmailType = "Received",
                        Timestamp = DateTime.UtcNow,
                        OutlookMessageId = "test-message-id-123"
                    },
                    Attachments = new List<AttachmentData>
                    {
                        new AttachmentData
                        {
                            FileName = "test.pdf",
                            OriginalFileName = "test.pdf",
                            ContentType = "application/pdf",
                            FileExtension = "pdf",
                            FileSize = 1024,
                            FileData = Convert.ToBase64String(new byte[1024])
                        }
                    }
                }
            };

            // Validate the request
            request.Should().NotBeNull();
            request.CorrelationId.Should().NotBeEmpty();
            request.Data.Email.Subject.Should().Be("Test Email");
            request.Data.Attachments.Should().HaveCount(1);
            request.Data.Attachments[0].FileName.Should().Be("test.pdf");
        }

        [Fact]
        public void ValidationUtilities_ShouldWorkCorrectly()
        {
            // Test file extension validation
            ValidationUtilities.IsFileExtensionAllowed(".pdf").Should().BeTrue();
            ValidationUtilities.IsFileExtensionAllowed(".exe").Should().BeFalse();
            ValidationUtilities.IsFileExtensionAllowed("").Should().BeFalse();

            // Test file size validation
            ValidationUtilities.IsFileSizeAcceptable(1024).Should().BeTrue();
            ValidationUtilities.IsFileSizeAcceptable(0).Should().BeFalse();
            ValidationUtilities.IsFileSizeAcceptable(200 * 1024 * 1024).Should().BeFalse(); // 200MB > 100MB limit

            // Test email validation
            ValidationUtilities.IsValidEmailAddress("<EMAIL>").Should().BeTrue();
            ValidationUtilities.IsValidEmailAddress("invalid-email").Should().BeFalse();
            ValidationUtilities.IsValidEmailAddress("").Should().BeFalse();

            // Test correlation ID validation
            ValidationUtilities.IsValidCorrelationId(Guid.NewGuid().ToString()).Should().BeTrue();
            ValidationUtilities.IsValidCorrelationId("invalid-guid").Should().BeFalse();

            // Test message type validation
            ValidationUtilities.IsSupportedMessageType("EmailProcessingRequest").Should().BeTrue();
            ValidationUtilities.IsSupportedMessageType("InvalidMessageType").Should().BeFalse();

            // Test email type validation
            ValidationUtilities.IsSupportedEmailType("Received").Should().BeTrue();
            ValidationUtilities.IsSupportedEmailType("Sent").Should().BeTrue();
            ValidationUtilities.IsSupportedEmailType("Invalid").Should().BeFalse();
        }

        [Fact]
        public void FileNameSanitization_ShouldWorkCorrectly()
        {
            // Test filename sanitization
            ValidationUtilities.SanitizeFileName("test.pdf").Should().Be("test.pdf");
            ValidationUtilities.SanitizeFileName("test<>.pdf").Should().Be("test__.pdf");
            ValidationUtilities.SanitizeFileName("").Should().Be("");
            ValidationUtilities.SanitizeFileName("   ").Should().Be("");
            ValidationUtilities.SanitizeFileName("   .   ").Should().Be("unnamed_file");
            ValidationUtilities.SanitizeFileName("   test   ").Should().Be("test");
        }

        [Fact]
        public void Base64Validation_ShouldWorkCorrectly()
        {
            // Test base64 validation
            var validBase64 = Convert.ToBase64String(new byte[] { 1, 2, 3, 4 });
            ValidationUtilities.IsValidBase64String(validBase64).Should().BeTrue();
            ValidationUtilities.IsValidBase64String("invalid-base64").Should().BeFalse();
            ValidationUtilities.IsValidBase64String("").Should().BeFalse();
        }

        [Fact]
        public void ResponseModel_ShouldBeCompatible()
        {
            // Test response model creation
            var response = new EmailProcessingResponse
            {
                CorrelationId = Guid.NewGuid(),
                Timestamp = DateTime.UtcNow,
                Success = true,
                EmailId = 123,
                Message = "Processing completed successfully",
                ProcessingResults = new List<AttachmentProcessingResult>
                {
                    new AttachmentProcessingResult
                    {
                        FileName = "test.pdf",
                        AttachmentId = 456,
                        Success = true,
                        LocalStoragePath = "C:\\Local\\test.pdf",
                        UncStoragePath = "\\\\server\\share\\test.pdf"
                    }
                }
            };

            // Validate the response
            response.Should().NotBeNull();
            response.Success.Should().BeTrue();
            response.EmailId.Should().Be(123);
            response.ProcessingResults.Should().HaveCount(1);
            response.ProcessingResults[0].Success.Should().BeTrue();
        }

        [Fact]
        public void TestConnectionModels_ShouldWorkCorrectly()
        {
            // Test connection models
            var testMessage = new TestConnectionMessage
            {
                CorrelationId = Guid.NewGuid(),
                Timestamp = DateTime.UtcNow
            };

            var testResponse = new TestConnectionResponse
            {
                CorrelationId = testMessage.CorrelationId,
                Timestamp = DateTime.UtcNow,
                Success = true,
                Message = "Service is running",
                ServiceVersion = "1.0.0"
            };

            // Validate the models
            testMessage.Should().NotBeNull();
            testMessage.MessageType.Should().Be("TestConnection");
            testResponse.Should().NotBeNull();
            testResponse.Success.Should().BeTrue();
            testResponse.ServiceVersion.Should().Be("1.0.0");
        }
    }
} 