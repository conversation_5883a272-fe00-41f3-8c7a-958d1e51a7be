using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using EmailProcessor.Domain.Interfaces;
using EmailProcessor.Domain.ValueObjects;
using EmailProcessor.Infrastructure.Logging;

namespace EmailProcessor.Infrastructure.Storage.LocalStorage
{
    public class LocalStorageProvider : IStorageProvider
    {
        private readonly string _basePath;
        private readonly ILoggingProvider _loggingProvider;
        private readonly int _maxRetries;
        private readonly int _retryDelayMs;

        public LocalStorageProvider(string basePath, ILoggingProvider loggingProvider = null, int maxRetries = 3, int retryDelayMs = 1000)
        {
            if (string.IsNullOrWhiteSpace(basePath))
                throw new ArgumentException("BasePath cannot be null or empty", nameof(basePath));

            _basePath = Path.GetFullPath(basePath);
            _loggingProvider = loggingProvider;
            _maxRetries = Math.Max(1, maxRetries);
            _retryDelayMs = Math.Max(100, retryDelayMs);
            
            // Ensure base directory exists
            try
            {
                if (!Directory.Exists(_basePath))
                {
                    Directory.CreateDirectory(_basePath);
                    _loggingProvider?.LogInformation($"Created local storage base directory: {_basePath}");
                }
            }
            catch (Exception ex)
            {
                _loggingProvider?.LogError($"Failed to create local storage base directory: {_basePath}", ex);
                throw new InvalidOperationException($"Cannot create local storage base directory: {_basePath}", ex);
            }
        }

        public StorageType StorageType => StorageType.Local;

        public string BasePath => _basePath;

        public async Task<FilePath> SaveFileAsync(byte[] fileData, string fileName, string directoryPath)
        {
            if (fileData == null)
                throw new ArgumentNullException(nameof(fileData));
            if (string.IsNullOrWhiteSpace(fileName))
                throw new ArgumentException("FileName cannot be null or empty", nameof(fileName));

            var fullPath = Path.Combine(_basePath, directoryPath ?? "", fileName);
            var directory = Path.GetDirectoryName(fullPath);

            return await ExecuteWithRetryAsync(async () =>
            {
                try
                {
                    // Create directory if it doesn't exist
                    if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                    {
                        Directory.CreateDirectory(directory);
                        _loggingProvider?.LogDebug($"Created directory: {directory}");
                    }

                    // Validate available space (basic check)
                    var availableSpace = await GetAvailableSpaceAsync();
                    if (availableSpace < fileData.Length * 2) // Need at least 2x file size for safety
                    {
                        throw new InvalidOperationException($"Insufficient disk space. Available: {availableSpace} bytes, Required: {fileData.Length * 2} bytes");
                    }

                    // Write file atomically using temp file
                    var tempPath = fullPath + ".tmp";
                    await File.WriteAllBytesAsync(tempPath, fileData);
                    
                    // Verify file was written correctly
                    var tempFileInfo = new FileInfo(tempPath);
                    if (tempFileInfo.Length != fileData.Length)
                    {
                        File.Delete(tempPath);
                        throw new InvalidOperationException($"File size mismatch after write. Expected: {fileData.Length}, Actual: {tempFileInfo.Length}");
                    }

                    // Move temp file to final location
                    if (File.Exists(fullPath))
                    {
                        File.Delete(fullPath);
                    }
                    File.Move(tempPath, fullPath);

                    _loggingProvider?.LogDebug($"Successfully saved file to local storage: {fullPath}");
                    return FilePath.Create(fullPath);
                }
                catch (Exception ex)
                {
                    _loggingProvider?.LogError($"Error saving file to local storage: {fullPath}", ex);
                    
                    // Clean up temp file if it exists
                    var tempPath = fullPath + ".tmp";
                    if (File.Exists(tempPath))
                    {
                        try { File.Delete(tempPath); } catch { }
                    }
                    
                    throw;
                }
            }, $"SaveFileAsync: {fileName}");
        }

        public async Task<FilePath> SaveFileAsync(Stream fileStream, string fileName, string directoryPath)
        {
            if (fileStream == null)
                throw new ArgumentNullException(nameof(fileStream));
            if (string.IsNullOrWhiteSpace(fileName))
                throw new ArgumentException("FileName cannot be null or empty", nameof(fileName));

            var fullPath = Path.Combine(_basePath, directoryPath ?? "", fileName);
            var directory = Path.GetDirectoryName(fullPath);

            return await ExecuteWithRetryAsync(async () =>
            {
                try
                {
                    // Create directory if it doesn't exist
                    if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                    {
                        Directory.CreateDirectory(directory);
                        _loggingProvider?.LogDebug($"Created directory: {directory}");
                    }

                    // Write file atomically using temp file
                    var tempPath = fullPath + ".tmp";
                    long bytesWritten = 0;

                    using (var tempFileStream = File.Create(tempPath))
                    {
                        var originalPosition = fileStream.Position;
                        fileStream.Position = 0; // Ensure we start from the beginning
                        
                        await fileStream.CopyToAsync(tempFileStream);
                        bytesWritten = tempFileStream.Length;
                        
                        fileStream.Position = originalPosition; // Restore original position
                    }

                    // Verify file was written
                    var tempFileInfo = new FileInfo(tempPath);
                    if (!tempFileInfo.Exists || tempFileInfo.Length == 0)
                    {
                        File.Delete(tempPath);
                        throw new InvalidOperationException($"Failed to write file or file is empty: {tempPath}");
                    }

                    // Move temp file to final location
                    if (File.Exists(fullPath))
                    {
                        File.Delete(fullPath);
                    }
                    File.Move(tempPath, fullPath);

                    _loggingProvider?.LogDebug($"Successfully saved file from stream to local storage: {fullPath}, Size: {bytesWritten} bytes");
                    return FilePath.Create(fullPath);
                }
                catch (Exception ex)
                {
                    _loggingProvider?.LogError($"Error saving file from stream to local storage: {fullPath}", ex);
                    
                    // Clean up temp file if it exists
                    var tempPath = fullPath + ".tmp";
                    if (File.Exists(tempPath))
                    {
                        try { File.Delete(tempPath); } catch { }
                    }
                    
                    throw;
                }
            }, $"SaveFileAsync (Stream): {fileName}");
        }

        public async Task CreateDirectoryAsync(string directoryPath)
        {
            var fullPath = Path.Combine(_basePath, directoryPath);
            if (!Directory.Exists(fullPath))
            {
                Directory.CreateDirectory(fullPath);
            }
            await Task.CompletedTask;
        }

        public async Task<bool> FileExistsAsync(string filePath)
        {
            var exists = File.Exists(filePath);
            await Task.CompletedTask;
            return exists;
        }

        public async Task<bool> DirectoryExistsAsync(string directoryPath)
        {
            var fullPath = Path.Combine(_basePath, directoryPath);
            var exists = Directory.Exists(fullPath);
            await Task.CompletedTask;
            return exists;
        }

        public async Task DeleteFileAsync(string filePath)
        {
            if (File.Exists(filePath))
            {
                File.Delete(filePath);
            }
            await Task.CompletedTask;
        }

        public async Task<FileInfo> GetFileInfoAsync(string filePath)
        {
            var fileInfo = new FileInfo(filePath);
            await Task.CompletedTask;
            return fileInfo;
        }

        public async Task<long> GetAvailableSpaceAsync()
        {
            var driveInfo = new DriveInfo(Path.GetPathRoot(_basePath));
            await Task.CompletedTask;
            return driveInfo.AvailableFreeSpace;
        }

        public async Task<bool> TestConnectivityAsync()
        {
            try
            {
                var testFile = Path.Combine(_basePath, $"test_{Guid.NewGuid()}.tmp");
                await File.WriteAllTextAsync(testFile, "test");
                File.Delete(testFile);
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Executes an operation with retry logic for transient errors
        /// </summary>
        private async Task<T> ExecuteWithRetryAsync<T>(Func<Task<T>> operation, string operationName)
        {
            var attempt = 1;
            Exception lastException = null;

            while (attempt <= _maxRetries)
            {
                try
                {
                    return await operation();
                }
                catch (Exception ex) when (IsTransientError(ex) && attempt < _maxRetries)
                {
                    lastException = ex;
                    _loggingProvider?.LogWarning($"Transient error in {operationName} (attempt {attempt}/{_maxRetries}): {ex.Message}");
                    
                    // Exponential backoff with jitter
                    var delay = _retryDelayMs * (int)Math.Pow(2, attempt - 1);
                    var jitter = new Random().Next(0, delay / 4); // Add up to 25% jitter
                    await Task.Delay(delay + jitter);
                    
                    attempt++;
                }
                catch (Exception ex)
                {
                    _loggingProvider?.LogError($"Non-transient error in {operationName}: {ex.Message}", ex);
                    throw;
                }
            }

            _loggingProvider?.LogError($"Operation {operationName} failed after {_maxRetries} attempts", lastException);
            throw lastException ?? new InvalidOperationException($"Operation {operationName} failed after {_maxRetries} attempts");
        }

        /// <summary>
        /// Determines if an exception represents a transient error that should be retried
        /// </summary>
        private static bool IsTransientError(Exception ex)
        {
            return ex is IOException ||
                   ex is UnauthorizedAccessException ||
                   ex is DirectoryNotFoundException ||
                   (ex is SystemException && ex.Message.Contains("being used by another process"));
        }
    }
} 