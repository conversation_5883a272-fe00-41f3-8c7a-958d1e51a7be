namespace EmailProcessor.Domain.ValueObjects
{
    /// <summary>
    /// Enum representing the log level for processing logs
    /// </summary>
    public enum LogLevel : byte
    {
        /// <summary>
        /// Debug level logging
        /// </summary>
        Debug = 1,
        
        /// <summary>
        /// Information level logging
        /// </summary>
        Information = 2,
        
        /// <summary>
        /// Warning level logging
        /// </summary>
        Warning = 3,
        
        /// <summary>
        /// Error level logging
        /// </summary>
        Error = 4,
        
        /// <summary>
        /// Fatal level logging
        /// </summary>
        Fatal = 5
    }
} 