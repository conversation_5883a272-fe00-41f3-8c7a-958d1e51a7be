using System;
using EmailProcessor.Domain.Entities;
using EmailProcessor.Domain.ValueObjects;

namespace EmailProcessor.Domain.Events
{
    /// <summary>
    /// Domain event raised when an attachment's local processing status changes
    /// </summary>
    public class AttachmentLocalProcessingStatusChangedEvent : IDomainEvent
    {
        public Attachment Attachment { get; }
        public ProcessingStatus NewStatus { get; }
        public DateTime OccurredOn { get; }

        public AttachmentLocalProcessingStatusChangedEvent(Attachment attachment, ProcessingStatus newStatus)
        {
            Attachment = attachment ?? throw new ArgumentNullException(nameof(attachment));
            NewStatus = newStatus;
            OccurredOn = DateTime.UtcNow;
        }
    }
} 