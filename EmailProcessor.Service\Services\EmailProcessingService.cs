using EmailProcessor.Domain.Entities;
using EmailProcessor.Domain.Interfaces;
using EmailProcessor.Domain.ValueObjects;
using EmailProcessor.Shared.Models;
using EmailProcessor.Infrastructure.Storage.DualStorageService;
using EmailProcessor.Infrastructure.Logging;

namespace EmailProcessor.Service.Services
{
    /// <summary>
    /// Service responsible for orchestrating email processing workflow
    /// </summary>
    public class EmailProcessingService
    {
        private readonly IEmailRepository _emailRepository;
        private readonly IAttachmentRepository _attachmentRepository;
        private readonly IProcessingLogRepository _processingLogRepository;
        private readonly ILoggingProvider _loggingProvider;
        private readonly AttachmentHandlerService _attachmentHandlerService;
        private readonly DualStorageService _dualStorageService;

        public EmailProcessingService(
            IEmailRepository emailRepository,
            IAttachmentRepository attachmentRepository,
            IProcessingLogRepository processingLogRepository,
            ILoggingProvider loggingProvider,
            AttachmentHandlerService attachmentHandlerService,
            DualStorageService dualStorageService)
        {
            _emailRepository = emailRepository ?? throw new ArgumentNullException(nameof(emailRepository));
            _attachmentRepository = attachmentRepository ?? throw new ArgumentNullException(nameof(attachmentRepository));
            _processingLogRepository = processingLogRepository ?? throw new ArgumentNullException(nameof(processingLogRepository));
            _loggingProvider = loggingProvider ?? throw new ArgumentNullException(nameof(loggingProvider));
            _attachmentHandlerService = attachmentHandlerService ?? throw new ArgumentNullException(nameof(attachmentHandlerService));
            _dualStorageService = dualStorageService ?? throw new ArgumentNullException(nameof(dualStorageService));
        }

        public async Task<EmailProcessingResponse> ProcessEmailAsync(EmailProcessingRequest request)
        {
            var correlationId = request.CorrelationId;
            var response = new EmailProcessingResponse
            {
                CorrelationId = correlationId,
                Timestamp = DateTime.UtcNow
            };

            try
            {
                await _loggingProvider.LogInformationAsync($"Starting email processing (CorrelationId: {correlationId})", "EmailProcessingService", correlationId);

                // Validate request
                if (!ValidateRequest(request))
                {
                    response.Success = false;
                    response.Message = "Invalid request data";
                    await _loggingProvider.LogErrorAsync($"Invalid request data (CorrelationId: {correlationId})", "EmailProcessingService", null, correlationId);
                    return response;
                }

                // Check for duplicate email
                var existingEmail = await _emailRepository.GetByOutlookMessageIdAsync(request.Data.Email.OutlookMessageId);
                if (existingEmail != null)
                {
                    response.Success = false;
                    response.Message = "Email already processed";
                    response.EmailId = existingEmail.EmailId;
                    await _loggingProvider.LogWarningAsync($"Email already processed: {request.Data.Email.OutlookMessageId} (CorrelationId: {correlationId})", "EmailProcessingService", correlationId);
                    return response;
                }

                // Create email entity
                var email = await CreateEmailEntityAsync(request.Data.Email, correlationId);
                if (email == null)
                {
                    response.Success = false;
                    response.Message = "Failed to create email entity";
                    await _loggingProvider.LogErrorAsync($"Failed to create email entity (CorrelationId: {correlationId})", "EmailProcessingService", null, correlationId);
                    return response;
                }

                // Save email to database
                email = await _emailRepository.AddAsync(email);

                response.EmailId = email.EmailId;
                await _loggingProvider.LogInformationAsync($"Email saved to database with ID: {email.EmailId} (CorrelationId: {correlationId})", "EmailProcessingService", correlationId);

                // Process attachments
                var attachmentResults = await ProcessAttachmentsAsync(email, request.Data.Attachments, correlationId);
                response.ProcessingResults = attachmentResults.Select(r => new EmailProcessor.Shared.Models.AttachmentProcessingResult
                {
                    FileName = r.FileName,
                    AttachmentId = r.AttachmentId,
                    Success = r.Success,
                    LocalStoragePath = r.LocalStoragePath,
                    UncStoragePath = r.UncStoragePath,
                    ErrorMessage = r.ErrorMessage
                }).ToList();

                // Update email processing status
                var allAttachmentsSuccessful = attachmentResults.All(r => r.Success);
                email.UpdateProcessingStatus(allAttachmentsSuccessful ? ProcessingStatus.Completed : ProcessingStatus.Failed);
                await _emailRepository.UpdateAsync(email);

                response.Success = allAttachmentsSuccessful;
                response.Message = allAttachmentsSuccessful ? "Email processing completed successfully" : "Email processing completed with errors";

                await _loggingProvider.LogInformationAsync($"Email processing completed. Success: {response.Success} (CorrelationId: {correlationId})", "EmailProcessingService", correlationId);
            }
            catch (Exception ex)
            {
                response.Success = false;
                response.Message = "Email processing failed";
                response.ErrorDetails = ex.Message;

                await _loggingProvider.LogErrorAsync($"Email processing failed (CorrelationId: {correlationId})",  "EmailProcessingService", ex, correlationId);
            }

            return response;
        }

        private bool ValidateRequest(EmailProcessingRequest request)
        {
            if (request?.Data?.Email == null)
                return false;

            var email = request.Data.Email;
            return !string.IsNullOrWhiteSpace(email.Subject) &&
                   !string.IsNullOrWhiteSpace(email.OutlookMessageId) &&
                   !string.IsNullOrWhiteSpace(email.SenderEmail) &&
                   email.Timestamp != default;
        }

        private async Task<Email?> CreateEmailEntityAsync(EmailData emailData, Guid correlationId)
        {
            try
            {
                // Parse email type
                var emailType = emailData.EmailType.ToLower() switch
                {
                    "sent" => EmailType.Sent,
                    "received" => EmailType.Received,
                    _ => EmailType.Received
                };

                // Create email addresses
                var senderEmail = EmailAddress.Create(emailData.SenderEmail);
                var recipientTo = emailData.RecipientTo.Select(EmailAddress.Create).ToList();
                var recipientCC = emailData.RecipientCC.Select(EmailAddress.Create).ToList();

                // Create email entity
                var email = new Email(
                    emailData.Subject,
                    emailData.SenderName,
                    senderEmail,
                    recipientTo,
                    recipientCC,
                    emailType,
                    emailData.Timestamp,
                    emailData.OutlookMessageId);

                await _loggingProvider.LogDebugAsync($"Email entity created for: {emailData.Subject} (CorrelationId: {correlationId})", "EmailProcessingService", correlationId);

                return email;
            }
            catch (Exception ex)
            {
                await _loggingProvider.LogErrorAsync($"Failed to create email entity for: {emailData.Subject} (CorrelationId: {correlationId})", "EmailProcessingService", ex, correlationId);
                return null;
            }
        }

        private async Task<List<AttachmentProcessingResult>> ProcessAttachmentsAsync(
            Email email, 
            List<AttachmentData> attachments, 
            Guid correlationId)
        {
            var results = new List<AttachmentProcessingResult>();

            foreach (var attachmentData in attachments)
            {
                var result = new AttachmentProcessingResult
                {
                    FileName = attachmentData.FileName,
                    Success = false
                };

                try
                {
                    await _loggingProvider.LogDebugAsync($"Processing attachment: {attachmentData.FileName} (CorrelationId: {correlationId})", "EmailProcessingService", correlationId);

                    // Create attachment entity
                    var attachment = await CreateAttachmentEntityAsync(email, attachmentData, correlationId);
                    if (attachment == null)
                    {
                        result.ErrorMessage = "Failed to create attachment entity";
                        results.Add(result);
                        continue;
                    }

                    // Save attachment to database
                    attachment = await _attachmentRepository.AddAsync(attachment);
                    result.AttachmentId = attachment.AttachmentId;

                    // Process storage
                    var storageResult = await ProcessAttachmentStorageAsync(attachment, attachmentData, correlationId);
                    
                    if (storageResult.IsSuccessful)
                    {
                        // Update attachment with storage paths
                        if (storageResult.LocalStorageResult.IsSuccessful)
                        {
                            attachment.SetLocalStoragePath(FilePath.Create(storageResult.LocalStorageResult.FilePath));
                            attachment.UpdateLocalProcessingStatus(ProcessingStatus.Completed);
                        }

                        if (storageResult.UncStorageResult.IsSuccessful)
                        {
                            attachment.SetUncStoragePath(FilePath.Create(storageResult.UncStorageResult.FilePath));
                            attachment.UpdateUncProcessingStatus(ProcessingStatus.Completed);
                        }
                        
                        result.Success = true;
                        result.LocalStoragePath = storageResult.LocalStorageResult.FilePath;
                        result.UncStoragePath = storageResult.UncStorageResult.FilePath;
                    }
                    else
                    {
                        // Update attachment with failure status
                        attachment.UpdateLocalProcessingStatus(storageResult.LocalStorageResult.IsSuccessful ? ProcessingStatus.Completed : ProcessingStatus.Failed);
                        attachment.UpdateUncProcessingStatus(storageResult.UncStorageResult.IsSuccessful ? ProcessingStatus.Completed : ProcessingStatus.Failed);
                        
                        result.Success = storageResult.LocalStorageResult.IsSuccessful || storageResult.UncStorageResult.IsSuccessful;
                        result.LocalStoragePath = storageResult.LocalStorageResult.FilePath;
                        result.UncStoragePath = storageResult.UncStorageResult.FilePath;
                        result.ErrorMessage = storageResult.ErrorMessage;
                    }

                    // Update attachment in database
                    await _attachmentRepository.UpdateAsync(attachment);

                    await _loggingProvider.LogInformationAsync($"Attachment processed: {attachmentData.FileName}, Success: {result.Success} (CorrelationId: {correlationId})", "EmailProcessingService", correlationId);
                }
                catch (Exception ex)
                {
                    result.ErrorMessage = ex.Message;
                    await _loggingProvider.LogErrorAsync($"Failed to process attachment: {attachmentData.FileName} (CorrelationId: {correlationId})", "EmailProcessingService", ex, correlationId);
                }

                results.Add(result);
            }

            return results;
        }

        private async Task<Attachment?> CreateAttachmentEntityAsync(Email email, AttachmentData attachmentData, Guid correlationId)
        {
            try
            {
                // Validate file extension
                if (!string.IsNullOrWhiteSpace(attachmentData.FileExtension))
                {
                    var extension = attachmentData.FileExtension.ToLowerInvariant();
                    if (extension.StartsWith("."))
                    {
                        extension = extension.Substring(1);
                    }
                    attachmentData.FileExtension = extension;
                }

                // Create attachment entity
                var attachment = new Attachment(
                    email.EmailId,
                    attachmentData.FileName,
                    attachmentData.OriginalFileName,
                    attachmentData.ContentType,
                    attachmentData.FileExtension,
                    attachmentData.FileSize);

                await _loggingProvider.LogDebugAsync($"Attachment entity created for: {attachmentData.FileName} (CorrelationId: {correlationId})", "EmailProcessingService", correlationId);

                return attachment;
            }
            catch (Exception ex)
            {
                await _loggingProvider.LogErrorAsync($"Failed to create attachment entity for: {attachmentData.FileName} (CorrelationId: {correlationId})", "EmailProcessingService", ex, correlationId);
                return null;
            }
        }

        private async Task<DualStorageResult> ProcessAttachmentStorageAsync(
            Attachment attachment, 
            AttachmentData attachmentData, 
            Guid correlationId)
        {
            try
            {
                // Convert base64 data to bytes
                var fileBytes = Convert.FromBase64String(attachmentData.FileData);

                // Create directory structure
                var directoryStructure = CreateDirectoryStructure(attachment.Email.EmailType, attachment.Email.Timestamp);

                // Process with dual storage service
                var result = await _dualStorageService.SaveFileAsync(
                    attachmentData.FileName,
                    fileBytes,
                    directoryStructure);

                return result;
            }
            catch (Exception ex)
            {
                await _loggingProvider.LogErrorAsync($"Failed to process attachment storage for: {attachmentData.FileName} (CorrelationId: {correlationId})", "EmailProcessingService", ex, correlationId);
                
                return new DualStorageResult
                {
                    FileName = attachmentData.FileName,
                    IsSuccessful = false,
                    ErrorMessage = ex.Message,
                    LocalStorageResult = new StorageResult { StorageType = StorageType.Local, IsSuccessful = false, ErrorMessage = ex.Message },
                    UncStorageResult = new StorageResult { StorageType = StorageType.Unc, IsSuccessful = false, ErrorMessage = ex.Message }
                };
            }
        }

        private string CreateDirectoryStructure(EmailType emailType, DateTime timestamp)
        {
            return $"{timestamp.Year}\\{timestamp.Month:D2}\\{timestamp.Day:D2}\\{emailType}";
        }
    }
} 