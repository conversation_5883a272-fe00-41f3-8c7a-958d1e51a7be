// This file is now deprecated. Use EmailProcessor.Shared.Models instead.
// Keeping this file for backward compatibility during transition.

using EmailProcessor.Shared.Models;

namespace EmailProcessor.AddIn.Models
{
    // Re-export shared models for backward compatibility
    public class ProcessingMessage : EmailProcessor.Shared.Models.EmailProcessingRequest { }
    public class ProcessingResponse : EmailProcessor.Shared.Models.EmailProcessingResponse { }
    public class AttachmentProcessingResult : EmailProcessor.Shared.Models.AttachmentProcessingResult { }
} 