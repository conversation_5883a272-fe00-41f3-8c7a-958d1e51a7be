using System;
using EmailProcessor.Domain.ValueObjects;

namespace EmailProcessor.Domain.Entities
{
    /// <summary>
    /// Core ProcessingLog entity representing processing log entries in the domain
    /// </summary>
    public class ProcessingLog
    {
        public long LogId { get; private set; }
        public long? EmailId { get; private set; }
        public long? AttachmentId { get; private set; }
        public LogLevel LogLevel { get; private set; }
        public string Message { get; private set; }
        public string ExceptionDetails { get; private set; }
        public string SourceComponent { get; private set; }
        public Guid? CorrelationId { get; private set; }
        public DateTime CreatedDate { get; private set; }
        
        // Navigation properties
        public virtual Email Email { get; private set; }
        public virtual Attachment Attachment { get; private set; }

        // Private constructor for EF Core
        private ProcessingLog() { }

        public ProcessingLog(
            LogLevel logLevel,
            string message,
            string sourceComponent,
            Guid? correlationId = null,
            long? emailId = null,
            long? attachmentId = null,
            string exceptionDetails = null)
        {
            ValidateProcessingLog(logLevel, message, sourceComponent);
            
            LogLevel = logLevel;
            Message = message;
            ExceptionDetails = exceptionDetails;
            SourceComponent = sourceComponent;
            CorrelationId = correlationId;
            EmailId = emailId;
            AttachmentId = attachmentId;
            CreatedDate = DateTime.UtcNow;
        }

        public static ProcessingLog CreateInfo(
            string message,
            string sourceComponent,
            Guid? correlationId = null,
            long? emailId = null,
            long? attachmentId = null)
        {
            return new ProcessingLog(LogLevel.Information, message, sourceComponent, correlationId, emailId, attachmentId);
        }

        public static ProcessingLog CreateWarning(
            string message,
            string sourceComponent,
            Guid? correlationId = null,
            long? emailId = null,
            long? attachmentId = null)
        {
            return new ProcessingLog(LogLevel.Warning, message, sourceComponent, correlationId, emailId, attachmentId);
        }

        public static ProcessingLog CreateError(
            string message,
            string sourceComponent,
            Exception exception = null,
            Guid? correlationId = null,
            long? emailId = null,
            long? attachmentId = null)
        {
            var exceptionDetails = exception?.ToString();
            return new ProcessingLog(LogLevel.Error, message, sourceComponent, correlationId, emailId, attachmentId, exceptionDetails);
        }

        public static ProcessingLog CreateDebug(
            string message,
            string sourceComponent,
            Guid? correlationId = null,
            long? emailId = null,
            long? attachmentId = null)
        {
            return new ProcessingLog(LogLevel.Debug, message, sourceComponent, correlationId, emailId, attachmentId);
        }

        public static ProcessingLog CreateFatal(
            string message,
            string sourceComponent,
            Exception exception = null,
            Guid? correlationId = null,
            long? emailId = null,
            long? attachmentId = null)
        {
            var exceptionDetails = exception?.ToString();
            return new ProcessingLog(LogLevel.Fatal, message, sourceComponent, correlationId, emailId, attachmentId, exceptionDetails);
        }

        private void ValidateProcessingLog(LogLevel logLevel, string message, string sourceComponent)
        {
            if (string.IsNullOrWhiteSpace(message))
                throw new ArgumentException("Log message cannot be null or empty", nameof(message));
                
            if (string.IsNullOrWhiteSpace(sourceComponent))
                throw new ArgumentException("Source component cannot be null or empty", nameof(sourceComponent));
        }

        public bool IsError => LogLevel == LogLevel.Error || LogLevel == LogLevel.Fatal;
        public bool IsWarning => LogLevel == LogLevel.Warning;
        public bool IsInfo => LogLevel == LogLevel.Information;
        public bool IsDebug => LogLevel == LogLevel.Debug;
    }
} 