using System;
using System.IO;
using System.Threading.Tasks;
using EmailProcessor.Domain.ValueObjects;
using EmailProcessor.Infrastructure.Logging;
using FluentAssertions;
using Xunit;

namespace EmailProcessor.Infrastructure.Tests.Logging
{
    public class SerilogLoggingProviderTests : IDisposable
    {
        private readonly string _testLogDirectory;
        private readonly SerilogLoggingProvider _loggingProvider;

        public SerilogLoggingProviderTests()
        {
            _testLogDirectory = Path.Combine(Path.GetTempPath(), "EmailProcessor_LogTests", Guid.NewGuid().ToString());
            Directory.CreateDirectory(_testLogDirectory);

            var config = new SerilogLoggingConfiguration
            {
                LogFilePath = Path.Combine(_testLogDirectory, "test-.log"),
                EnableConsoleLogging = false, // Disable for tests
                EnableFileLogging = true,
                MinimumLevel = LogLevel.Debug,
                UseStructuredLogging = false
            };

            _loggingProvider = new SerilogLoggingProvider(config);
        }

        public void Dispose()
        {
            _loggingProvider?.Dispose();

            // Clean up test directory
            if (Directory.Exists(_testLogDirectory))
            {
                try
                {
                    Directory.Delete(_testLogDirectory, true);
                }
                catch
                {
                    // Ignore cleanup errors in tests
                }
            }
        }

        [Fact]
        public void Constructor_WithDefaultConfiguration_CreatesInstance()
        {
            // Arrange & Act
            using var provider = new SerilogLoggingProvider();

            // Assert
            provider.Should().NotBeNull();
            provider.CurrentLogLevel.Should().Be(LogLevel.Information);
        }

        [Fact]
        public void Constructor_WithCustomConfiguration_UsesCustomSettings()
        {
            // Arrange
            var config = new SerilogLoggingConfiguration
            {
                MinimumLevel = LogLevel.Warning,
                EnableConsoleLogging = false,
                EnableFileLogging = true,
                UseStructuredLogging = true
            };

            // Act
            using var provider = new SerilogLoggingProvider(config);

            // Assert
            provider.Should().NotBeNull();
            provider.CurrentLogLevel.Should().Be(LogLevel.Warning);
        }

        [Fact]
        public void SetLogLevel_ChangesCurrentLogLevel()
        {
            // Arrange
            var newLogLevel = LogLevel.Error;

            // Act
            _loggingProvider.SetLogLevel(newLogLevel);

            // Assert
            _loggingProvider.CurrentLogLevel.Should().Be(newLogLevel);
        }

        [Theory]
        [InlineData(LogLevel.Debug)]
        [InlineData(LogLevel.Information)]
        [InlineData(LogLevel.Warning)]
        [InlineData(LogLevel.Error)]
        [InlineData(LogLevel.Fatal)]
        public void IsEnabled_WithDifferentLogLevels_ReturnsCorrectValue(LogLevel testLevel)
        {
            // Arrange
            _loggingProvider.SetLogLevel(LogLevel.Information);

            // Act
            var result = _loggingProvider.IsEnabled(testLevel);

            // Assert
            var expected = testLevel >= LogLevel.Information;
            result.Should().Be(expected);
        }

        [Fact]
        public async Task LogAsync_WithDebugLevel_LogsMessage()
        {
            // Arrange
            var message = "Debug test message";
            var sourceComponent = "TestComponent";
            var correlationId = Guid.NewGuid();

            // Act
            await _loggingProvider.LogAsync(LogLevel.Debug, message, sourceComponent, null, correlationId);

            // Assert - Give some time for file writing
            await Task.Delay(100);

            // Check if log file was created (basic verification)
            var logFiles = Directory.GetFiles(_testLogDirectory, "*.log");
            logFiles.Should().NotBeEmpty();
        }

        [Fact]
        public async Task LogAsync_WithException_LogsExceptionDetails()
        {
            // Arrange
            var message = "Error test message";
            var sourceComponent = "TestComponent";
            var exception = new InvalidOperationException("Test exception");
            var correlationId = Guid.NewGuid();

            // Act
            await _loggingProvider.LogAsync(LogLevel.Error, message, sourceComponent, exception, correlationId);

            // Assert - Give some time for file writing
            await Task.Delay(100);

            // Check if log file was created
            var logFiles = Directory.GetFiles(_testLogDirectory, "*.log");
            logFiles.Should().NotBeEmpty();
        }

        [Fact]
        public async Task LogDebugAsync_LogsDebugMessage()
        {
            // Arrange
            var message = "Debug message";
            var sourceComponent = "TestComponent";
            var correlationId = Guid.NewGuid();

            // Act
            await _loggingProvider.LogDebugAsync(message, sourceComponent, correlationId);

            // Assert
            await Task.Delay(100);
            var logFiles = Directory.GetFiles(_testLogDirectory, "*.log");
            logFiles.Should().NotBeEmpty();
        }

        [Fact]
        public async Task LogInformationAsync_LogsInformationMessage()
        {
            // Arrange
            var message = "Information message";
            var sourceComponent = "TestComponent";
            var correlationId = Guid.NewGuid();

            // Act
            await _loggingProvider.LogInformationAsync(message, sourceComponent, correlationId);

            // Assert
            await Task.Delay(100);
            var logFiles = Directory.GetFiles(_testLogDirectory, "*.log");
            logFiles.Should().NotBeEmpty();
        }

        [Fact] 
        public async Task LogWarningAsync_LogsWarningMessage()
        {
            // Arrange
            var message = "Warning message";
            var sourceComponent = "TestComponent";
            var correlationId = Guid.NewGuid();

            // Act
            await _loggingProvider.LogWarningAsync(message, sourceComponent, correlationId);

            // Assert
            await Task.Delay(100);
            var logFiles = Directory.GetFiles(_testLogDirectory, "*.log");
            logFiles.Should().NotBeEmpty();
        }

        [Fact]
        public async Task LogErrorAsync_LogsErrorMessage()
        {
            // Arrange
            var message = "Error message";
            var sourceComponent = "TestComponent";
            var exception = new Exception("Test exception");
            var correlationId = Guid.NewGuid();

            // Act
            await _loggingProvider.LogErrorAsync(message, sourceComponent, exception, correlationId);

            // Assert
            await Task.Delay(100);
            var logFiles = Directory.GetFiles(_testLogDirectory, "*.log");
            logFiles.Should().NotBeEmpty();
        }

        [Fact]
        public async Task LogFatalAsync_LogsFatalMessage()
        {
            // Arrange
            var message = "Fatal message";
            var sourceComponent = "TestComponent";
            var exception = new Exception("Fatal exception");
            var correlationId = Guid.NewGuid();

            // Act
            await _loggingProvider.LogFatalAsync(message, sourceComponent, exception, correlationId);

            // Assert
            await Task.Delay(100);
            var logFiles = Directory.GetFiles(_testLogDirectory, "*.log");
            logFiles.Should().NotBeEmpty();
        }

        [Fact]
        public async Task LogAsync_BelowMinimumLevel_DoesNotLog()
        {
            // Arrange
            _loggingProvider.SetLogLevel(LogLevel.Error);
            var message = "Debug message that should not be logged";
            var sourceComponent = "TestComponent";

            // Act
            await _loggingProvider.LogAsync(LogLevel.Debug, message, sourceComponent);

            // Assert
            await Task.Delay(100);
            
            // Since the log level is Error and we're trying to log Debug, no file should be created
            // or if created, it should not contain our debug message
            var logFiles = Directory.GetFiles(_testLogDirectory, "*.log");
            
            if (logFiles.Length > 0)
            {
                var logContent = await File.ReadAllTextAsync(logFiles[0]);
                logContent.Should().NotContain(message);
            }
        }

        [Fact]
        public void SerilogLoggingConfiguration_DefaultValues_AreCorrect()
        {
            // Arrange & Act
            var config = new SerilogLoggingConfiguration();

            // Assert
            config.MinimumLevel.Should().Be(LogLevel.Information);
            config.EnableConsoleLogging.Should().BeTrue();
            config.EnableFileLogging.Should().BeTrue();
            config.UseStructuredLogging.Should().BeFalse();
            config.RetainedFileCount.Should().Be(30);
            config.FileSizeLimitBytes.Should().Be(100 * 1024 * 1024); // 100MB
            config.FlushIntervalSeconds.Should().Be(5);
        }

        [Fact]
        public void SerilogLoggingConfiguration_CustomValues_AreSet()
        {
            // Arrange & Act
            var config = new SerilogLoggingConfiguration
            {
                MinimumLevel = LogLevel.Warning,
                EnableConsoleLogging = false,
                EnableFileLogging = true,
                UseStructuredLogging = true,
                RetainedFileCount = 60,
                FileSizeLimitBytes = 50 * 1024 * 1024,
                FlushIntervalSeconds = 10
            };

            // Assert
            config.MinimumLevel.Should().Be(LogLevel.Warning);
            config.EnableConsoleLogging.Should().BeFalse();
            config.EnableFileLogging.Should().BeTrue();
            config.UseStructuredLogging.Should().BeTrue();
            config.RetainedFileCount.Should().Be(60);
            config.FileSizeLimitBytes.Should().Be(50 * 1024 * 1024);
            config.FlushIntervalSeconds.Should().Be(10);
        }
    }
}