using System;
using System.Collections.Generic;
using EmailProcessor.AddIn.Models;
using EmailProcessor.Shared.Models;

namespace EmailProcessor.AddIn.Interfaces
{
    /// <summary>
    /// Interface for Outlook integration operations
    /// </summary>
    public interface IOutlookIntegration
    {
        /// <summary>
        /// Extracts email data from an Outlook MailItem
        /// </summary>
        /// <param name="mailItem">The Outlook MailItem to extract data from</param>
        /// <returns>EmailData object containing extracted information</returns>
        EmailProcessor.Shared.Models.EmailData ExtractEmailData(dynamic mailItem);

        /// <summary>
        /// Extracts attachment data from an Outlook MailItem
        /// </summary>
        /// <param name="mailItem">The Outlook MailItem containing attachments</param>
        /// <param name="maxFileSize">Maximum file size to process in bytes</param>
        /// <returns>List of AttachmentData objects</returns>
        List<EmailProcessor.Shared.Models.AttachmentData> ExtractAttachmentData(dynamic mailItem, long maxFileSize = 100 * 1024 * 1024); // 100MB default

        /// <summary>
        /// Validates if an email should be processed
        /// </summary>
        /// <param name="mailItem">The Outlook MailItem to validate</param>
        /// <returns>True if the email should be processed</returns>
        bool ShouldProcessEmail(dynamic mailItem);

        /// <summary>
        /// Validates if an attachment should be processed
        /// </summary>
        /// <param name="attachment">The Outlook Attachment to validate</param>
        /// <returns>True if the attachment should be processed</returns>
        bool ShouldProcessAttachment(dynamic attachment);

        /// <summary>
        /// Gets the email type (Received or Sent) from a MailItem
        /// </summary>
        /// <param name="mailItem">The Outlook MailItem</param>
        /// <returns>Email type string</returns>
        string GetEmailType(dynamic mailItem);
    }
} 