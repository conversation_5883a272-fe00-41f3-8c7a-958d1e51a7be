using System.Threading.Tasks;

namespace EmailProcessor.Domain.Interfaces
{
    /// <summary>
    /// Interface for log management operations
    /// </summary>
    public interface ILogManagementService
    {
        /// <summary>
        /// Performs log cleanup based on retention policies
        /// </summary>
        Task PerformLogCleanupAsync();

        /// <summary>
        /// Archives old log files to a compressed format
        /// </summary>
        Task ArchiveOldLogsAsync();
    }
}