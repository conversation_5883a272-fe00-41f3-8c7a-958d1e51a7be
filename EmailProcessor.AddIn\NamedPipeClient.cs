using System;
using System.IO.Pipes;
using System.Text;
using System.Threading.Tasks;
using System.Text.Json;
using System.Threading;
using EmailProcessor.AddIn.Interfaces;
using EmailProcessor.AddIn.Models;
using EmailProcessor.Shared.Models;
using System.Collections.Generic;

namespace EmailProcessor.AddIn.Communication
{
    /// <summary>
    /// Enhanced Named Pipe client for communication with Windows Service
    /// Phase 4: Improved with retry logic, connection management, and error handling
    /// </summary>
    public class NamedPipeClient : INamedPipeClient, IDisposable
    {
        private readonly string _pipeName;
        private NamedPipeClientStream _pipeClient;
        private bool _disposed;
        private readonly object _lockObject = new object();
        private readonly SemaphoreSlim _connectionSemaphore = new SemaphoreSlim(1, 1);
        private readonly CancellationTokenSource _cancellationTokenSource = new CancellationTokenSource();

        public bool IsConnected => _pipeClient != null && _pipeClient.IsConnected && !_disposed;

        public event EventHandler ConnectionLost;
        public event EventHandler Connected;
        public event EventHandler<ConnectionErrorEventArgs> ConnectionError;

        public NamedPipeClient(string pipeName)
        {
            _pipeName = pipeName ?? throw new ArgumentNullException(nameof(pipeName));
        }

        public async Task<bool> ConnectAsync(int timeoutSeconds = 10)
        {
            if (_disposed)
                return false;

            await _connectionSemaphore.WaitAsync();
            try
            {
                if (IsConnected)
                    return true; // Already connected

                // Dispose existing connection if any
                await DisconnectAsync();

                // Create new pipe client
                _pipeClient = new NamedPipeClientStream(
                    ".", // Server name (local machine)
                    _pipeName,
                    PipeDirection.InOut,
                    PipeOptions.Asynchronous);

                // Connect with timeout
                var connectTask = _pipeClient.ConnectAsync(_cancellationTokenSource.Token);
                var timeoutTask = Task.Delay(TimeSpan.FromSeconds(timeoutSeconds), _cancellationTokenSource.Token);

                var completedTask = await Task.WhenAny(connectTask, timeoutTask);
                if (completedTask == timeoutTask)
                {
                    // Timeout occurred
                    await DisconnectAsync();
                    OnConnectionError(new ConnectionErrorEventArgs("Connection timeout"));
                    return false;
                }

                // Wait for connection to complete
                await connectTask;

                if (_pipeClient != null && _pipeClient.IsConnected)
                {
                    OnConnected();
                    return true;
                }

                OnConnectionError(new ConnectionErrorEventArgs("Failed to establish connection"));
                return false;
            }
            catch (OperationCanceledException)
            {
                OnConnectionError(new ConnectionErrorEventArgs("Connection was cancelled"));
                return false;
            }
            catch (Exception ex)
            {
                OnConnectionError(new ConnectionErrorEventArgs($"Connection error: {ex.Message}"));
                await DisconnectAsync();
                return false;
            }
            finally
            {
                _connectionSemaphore.Release();
            }
        }

        public async Task DisconnectAsync()
        {
            try
            {
                lock (_lockObject)
                {
                    if (_pipeClient != null)
                    {
                        if (_pipeClient.IsConnected)
                        {
                            _pipeClient.Disconnect();
                        }
                        _pipeClient.Dispose();
                        _pipeClient = null;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error during disconnect: {ex.Message}");
            }
        }

        public async Task<ProcessingResponse> SendMessageAsync(ProcessingMessage message)
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(NamedPipeClient));

            if (message == null)
                throw new ArgumentNullException(nameof(message));

            // Ensure connection
            if (!IsConnected)
            {
                var connected = await ConnectAsync();
                if (!connected)
                {
                    throw new InvalidOperationException("Failed to connect to Windows Service");
                }
            }

            try
            {
                // Serialize message
                var jsonMessage = JsonSerializer.Serialize(message, new JsonSerializerOptions
                {
                    WriteIndented = false,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });

                // Add message length prefix for proper framing
                var messageLength = Encoding.UTF8.GetByteCount(jsonMessage);
                var lengthBytes = BitConverter.GetBytes(messageLength);
                var fullMessage = new byte[lengthBytes.Length + messageLength];
                
                Array.Copy(lengthBytes, 0, fullMessage, 0, lengthBytes.Length);
                Array.Copy(Encoding.UTF8.GetBytes(jsonMessage), 0, fullMessage, lengthBytes.Length, messageLength);

                // Send message
                await _pipeClient.WriteAsync(fullMessage, 0, fullMessage.Length, _cancellationTokenSource.Token);
                await _pipeClient.FlushAsync(_cancellationTokenSource.Token);

                // Read response
                var response = await ReadResponseAsync();
                return response;
            }
            catch (OperationCanceledException)
            {
                throw new InvalidOperationException("Message sending was cancelled");
            }
            catch (Exception ex)
            {
                // Check if connection was lost
                if (!IsConnected)
                {
                    OnConnectionLost();
                }
                throw new InvalidOperationException($"Failed to send message: {ex.Message}", ex);
            }
        }

        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                if (!IsConnected)
                {
                    return await ConnectAsync();
                }

                // Send a simple ping message
                var pingMessage = new ProcessingMessage
                {
                    Data = new EmailProcessingData
                    {
                        Email = new EmailProcessor.Shared.Models.EmailData { Subject = "PING" },
                        Attachments = new List<EmailProcessor.Shared.Models.AttachmentData>()
                    }
                };

                var response = await SendMessageAsync(pingMessage);
                return response != null && response.Success;
            }
            catch
            {
                return false;
            }
        }

        private async Task<ProcessingResponse> ReadResponseAsync()
        {
            try
            {
                // Read message length (4 bytes)
                var lengthBuffer = new byte[4];
                var bytesRead = await _pipeClient.ReadAsync(lengthBuffer, 0, 4, _cancellationTokenSource.Token);
                
                if (bytesRead != 4)
                {
                    throw new InvalidOperationException("Failed to read message length");
                }

                var messageLength = BitConverter.ToInt32(lengthBuffer, 0);
                
                if (messageLength <= 0 || messageLength > 1024 * 1024) // 1MB limit
                {
                    throw new InvalidOperationException($"Invalid message length: {messageLength}");
                }

                // Read message content
                var messageBuffer = new byte[messageLength];
                var totalBytesRead = 0;
                
                while (totalBytesRead < messageLength)
                {
                    var read = await _pipeClient.ReadAsync(
                        messageBuffer, 
                        totalBytesRead, 
                        messageLength - totalBytesRead, 
                        _cancellationTokenSource.Token);
                    
                    if (read == 0)
                    {
                        throw new InvalidOperationException("Connection closed while reading response");
                    }
                    
                    totalBytesRead += read;
                }

                var jsonResponse = Encoding.UTF8.GetString(messageBuffer, 0, messageLength);
                var response = JsonSerializer.Deserialize<ProcessingResponse>(jsonResponse, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });

                return response ?? new ProcessingResponse { Success = false, Message = "Failed to deserialize response" };
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error reading response: {ex.Message}");
                throw new InvalidOperationException($"Failed to read response: {ex.Message}", ex);
            }
        }

        private void OnConnected()
        {
            Connected?.Invoke(this, EventArgs.Empty);
        }

        private void OnConnectionLost()
        {
            ConnectionLost?.Invoke(this, EventArgs.Empty);
        }

        private void OnConnectionError(ConnectionErrorEventArgs e)
        {
            ConnectionError?.Invoke(this, e);
        }

        public void Dispose()
        {
            if (_disposed)
                return;

            _disposed = true;
            _cancellationTokenSource.Cancel();
            _cancellationTokenSource.Dispose();
            _connectionSemaphore.Dispose();
            
            try
            {
                DisconnectAsync().Wait(1000); // Wait up to 1 second for disconnect
            }
            catch
            {
                // Ignore errors during disposal
            }
        }

        protected virtual void Dispose(bool disposing)
        {
            if (disposing)
            {
                Dispose();
            }
        }
    }

    /// <summary>
    /// Event arguments for connection errors
    /// </summary>
    public class ConnectionErrorEventArgs : EventArgs
    {
        public string ErrorMessage { get; }

        public ConnectionErrorEventArgs(string errorMessage)
        {
            ErrorMessage = errorMessage ?? throw new ArgumentNullException(nameof(errorMessage));
        }
    }
} 