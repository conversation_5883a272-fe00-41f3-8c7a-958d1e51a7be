namespace EmailProcessor.Domain.ValueObjects
{
    /// <summary>
    /// Enum representing the processing status of emails and attachments
    /// </summary>
    public enum ProcessingStatus : byte
    {
        /// <summary>
        /// Item is pending processing
        /// </summary>
        Pending = 1,
        
        /// <summary>
        /// Item is currently being processed
        /// </summary>
        Processing = 2,
        
        /// <summary>
        /// Item has been successfully processed
        /// </summary>
        Completed = 3,
        
        /// <summary>
        /// Item processing failed
        /// </summary>
        Failed = 4
    }
} 