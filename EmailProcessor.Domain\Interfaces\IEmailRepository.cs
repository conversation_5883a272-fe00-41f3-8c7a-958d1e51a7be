using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EmailProcessor.Domain.Entities;
using EmailProcessor.Domain.ValueObjects;

namespace EmailProcessor.Domain.Interfaces
{
    /// <summary>
    /// Repository interface for Email entities
    /// </summary>
    public interface IEmailRepository
    {
        /// <summary>
        /// Gets an email by its ID
        /// </summary>
        Task<Email> GetByIdAsync(long emailId);

        /// <summary>
        /// Gets an email by its Outlook message ID
        /// </summary>
        Task<Email> GetByOutlookMessageIdAsync(string outlookMessageId);

        /// <summary>
        /// Gets all emails with optional filtering
        /// </summary>
        Task<IEnumerable<Email>> GetAllAsync(
            EmailType? emailType = null,
            ProcessingStatus? processingStatus = null,
            DateTime? fromDate = null,
            DateTime? toDate = null);

        /// <summary>
        /// Gets emails by processing status
        /// </summary>
        Task<IEnumerable<Email>> GetByProcessingStatusAsync(ProcessingStatus status);

        /// <summary>
        /// Gets emails by type (Received/Sent)
        /// </summary>
        Task<IEnumerable<Email>> GetByEmailTypeAsync(EmailType emailType);

        /// <summary>
        /// Gets emails within a date range
        /// </summary>
        Task<IEnumerable<Email>> GetByDateRangeAsync(DateTime fromDate, DateTime toDate);

        /// <summary>
        /// Adds a new email
        /// </summary>
        Task<Email> AddAsync(Email email);

        /// <summary>
        /// Updates an existing email
        /// </summary>
        Task<Email> UpdateAsync(Email email);

        /// <summary>
        /// Deletes an email
        /// </summary>
        Task DeleteAsync(long emailId);

        /// <summary>
        /// Checks if an email with the given Outlook message ID exists
        /// </summary>
        Task<bool> ExistsAsync(string outlookMessageId);

        /// <summary>
        /// Gets the count of emails with a specific processing status
        /// </summary>
        Task<int> GetCountByProcessingStatusAsync(ProcessingStatus status);

        /// <summary>
        /// Gets the count of emails by type
        /// </summary>
        Task<int> GetCountByEmailTypeAsync(EmailType emailType);
    }
} 