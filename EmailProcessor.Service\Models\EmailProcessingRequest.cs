// This file is now deprecated. Use EmailProcessor.Shared.Models instead.
// Keeping this file for backward compatibility during transition.

using EmailProcessor.Shared.Models;

namespace EmailProcessor.Service.Models
{
    // Re-export shared models for backward compatibility
    public class EmailProcessingRequest : EmailProcessor.Shared.Models.EmailProcessingRequest { }
    public class EmailProcessingData : EmailProcessor.Shared.Models.EmailProcessingData { }
    public class EmailData : EmailProcessor.Shared.Models.EmailData { }
    public class AttachmentData : EmailProcessor.Shared.Models.AttachmentData { }
    public class EmailProcessingResponse : EmailProcessor.Shared.Models.EmailProcessingResponse { }
    public class AttachmentProcessingResult : EmailProcessor.Shared.Models.AttachmentProcessingResult { }
} 