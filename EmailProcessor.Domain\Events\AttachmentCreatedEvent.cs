using System;
using EmailProcessor.Domain.Entities;

namespace EmailProcessor.Domain.Events
{
    /// <summary>
    /// Domain event raised when a new attachment is created
    /// </summary>
    public class AttachmentCreatedEvent : IDomainEvent
    {
        public Attachment Attachment { get; }
        public DateTime OccurredOn { get; }

        public AttachmentCreatedEvent(Attachment attachment)
        {
            Attachment = attachment ?? throw new ArgumentNullException(nameof(attachment));
            OccurredOn = DateTime.UtcNow;
        }
    }
} 