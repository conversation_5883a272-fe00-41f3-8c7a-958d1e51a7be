using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Win32;
using EmailProcessor.AddIn.Interfaces;
using System.IO;

namespace EmailProcessor.AddIn.Configuration
{
    /// <summary>
    /// Enhanced VSTO configuration management using registry storage
    /// Phase 4: Added additional configuration options and validation
    /// </summary>
    public class VSTOConfiguration : IVSTOConfiguration
    {
        private const string RegistryKeyPath = @"SOFTWARE\EmailProcessor\VSTO";
        private readonly Dictionary<string, string> _configuration;

        // Basic communication settings
        public string NamedPipeName { get; private set; } = "EmailProcessorPipe";
        public int ConnectionTimeoutSeconds { get; private set; } = 10;
        public int RetryCount { get; private set; } = 3;
        public int RetryDelaySeconds { get; private set; } = 2;
        public int MaxRetryDelaySeconds { get; private set; } = 30;

        // File processing settings
        public long MaxFileSizeBytes { get; private set; } = 100 * 1024 * 1024; // 100MB
        public List<string> ExcludedFileExtensions { get; private set; } = new List<string>();
        public List<string> AllowedFileExtensions { get; private set; } = new List<string>(); // Phase 4: Whitelist support
        public bool ProcessInlineImages { get; private set; } = false; // Phase 4: Control inline image processing
        public int MaxAttachmentCount { get; private set; } = 50; // Phase 4: Limit attachment count

        // Email processing settings
        public bool ProcessReceivedEmails { get; private set; } = true;
        public bool ProcessSentEmails { get; private set; } = true;
        public int MaxEmailAgeDays { get; private set; } = 30; // Phase 4: Skip old emails
        public List<string> ExcludedSenders { get; private set; } = new List<string>(); // Phase 4: Exclude specific senders
        public List<string> ExcludedDomains { get; private set; } = new List<string>(); // Phase 4: Exclude domains

        // Logging settings
        public string LogFilePath { get; private set; } = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "EmailProcessor", "VSTO.log");
        public string LogLevel { get; private set; } = "Information";
        public bool EnableDebugLogging { get; private set; } = false; // Phase 4: Debug logging control
        public int MaxLogFileSizeMB { get; private set; } = 10; // Phase 4: Log file size limit

        // Performance settings
        public int MaxConcurrentProcessing { get; private set; } = 5; // Phase 4: Concurrent processing limit
        public bool EnableAsyncProcessing { get; private set; } = true; // Phase 4: Async processing control
        public int ProcessingTimeoutSeconds { get; private set; } = 60; // Phase 4: Processing timeout

        // Security settings
        public bool ValidateFileSignatures { get; private set; } = false; // Phase 4: File signature validation
        public bool BlockExecutableFiles { get; private set; } = true; // Phase 4: Block executable files
        public List<string> BlockedFileTypes { get; private set; } = new List<string>(); // Phase 4: Blocked file types

        public VSTOConfiguration()
        {
            _configuration = new Dictionary<string, string>();
            LoadConfiguration();
        }

        public void LoadConfiguration()
        {
            try
            {
                using (var key = Registry.CurrentUser.OpenSubKey(RegistryKeyPath))
                {
                    if (key != null)
                    {
                        // Load basic communication settings
                        NamedPipeName = GetValue("NamedPipeName", "EmailProcessorPipe");
                        LoadNumericValue("ConnectionTimeoutSeconds", 10, value => ConnectionTimeoutSeconds = value);
                        LoadNumericValue("RetryCount", 3, value => RetryCount = value);
                        LoadNumericValue("RetryDelaySeconds", 2, value => RetryDelaySeconds = value);
                        LoadNumericValue("MaxRetryDelaySeconds", 30, value => MaxRetryDelaySeconds = value);

                        // Load file processing settings
                        LoadNumericValue("MaxFileSizeBytes", 104857600, value => MaxFileSizeBytes = value);
                        LoadNumericValue("MaxAttachmentCount", 50, value => MaxAttachmentCount = value);
                        ProcessInlineImages = LoadBooleanValue("ProcessInlineImages", false);

                        // Load email processing settings
                        ProcessReceivedEmails = LoadBooleanValue("ProcessReceivedEmails", true);
                        ProcessSentEmails = LoadBooleanValue("ProcessSentEmails", true);
                        LoadNumericValue("MaxEmailAgeDays", 30, value => MaxEmailAgeDays = value);

                        // Load logging settings
                        LogFilePath = GetValue("LogFilePath", LogFilePath);
                        LogLevel = GetValue("LogLevel", "Information");
                        EnableDebugLogging = LoadBooleanValue("EnableDebugLogging", false);
                        LoadNumericValue("MaxLogFileSizeMB", 10, value => MaxLogFileSizeMB = value);

                        // Load performance settings
                        LoadNumericValue("MaxConcurrentProcessing", 5, value => MaxConcurrentProcessing = value);
                        EnableAsyncProcessing = LoadBooleanValue("EnableAsyncProcessing", true);
                        LoadNumericValue("ProcessingTimeoutSeconds", 60, value => ProcessingTimeoutSeconds = value);

                        // Load security settings
                        ValidateFileSignatures = LoadBooleanValue("ValidateFileSignatures", false);
                        BlockExecutableFiles = LoadBooleanValue("BlockExecutableFiles", true);

                        // Load list values
                        LoadListValue("ExcludedFileExtensions", ".exe,.bat,.cmd,.scr,.pif,.com,.vbs,.js,.ps1", ExcludedFileExtensions);
                        LoadListValue("AllowedFileExtensions", "", AllowedFileExtensions); // Empty means all allowed
                        LoadListValue("ExcludedSenders", "", ExcludedSenders);
                        LoadListValue("ExcludedDomains", "noreply,no-reply,donotreply,do-not-reply", ExcludedDomains);
                        LoadListValue("BlockedFileTypes", ".exe,.bat,.cmd,.scr,.pif,.com,.vbs,.js,.ps1,.msi,.dll", BlockedFileTypes);
                    }
                    else
                    {
                        // Create default configuration
                        SaveConfiguration();
                    }
                }

                // Validate configuration
                ValidateConfiguration();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading configuration: {ex.Message}");
                // Use default values if registry access fails
            }
        }

        public void SaveConfiguration()
        {
            try
            {
                using (var key = Registry.CurrentUser.CreateSubKey(RegistryKeyPath))
                {
                    if (key != null)
                    {
                        // Save basic communication settings
                        key.SetValue("NamedPipeName", NamedPipeName);
                        key.SetValue("ConnectionTimeoutSeconds", ConnectionTimeoutSeconds.ToString());
                        key.SetValue("RetryCount", RetryCount.ToString());
                        key.SetValue("RetryDelaySeconds", RetryDelaySeconds.ToString());
                        key.SetValue("MaxRetryDelaySeconds", MaxRetryDelaySeconds.ToString());

                        // Save file processing settings
                        key.SetValue("MaxFileSizeBytes", MaxFileSizeBytes.ToString());
                        key.SetValue("MaxAttachmentCount", MaxAttachmentCount.ToString());
                        key.SetValue("ProcessInlineImages", ProcessInlineImages.ToString());

                        // Save email processing settings
                        key.SetValue("ProcessReceivedEmails", ProcessReceivedEmails.ToString());
                        key.SetValue("ProcessSentEmails", ProcessSentEmails.ToString());
                        key.SetValue("MaxEmailAgeDays", MaxEmailAgeDays.ToString());

                        // Save logging settings
                        key.SetValue("LogFilePath", LogFilePath);
                        key.SetValue("LogLevel", LogLevel);
                        key.SetValue("EnableDebugLogging", EnableDebugLogging.ToString());
                        key.SetValue("MaxLogFileSizeMB", MaxLogFileSizeMB.ToString());

                        // Save performance settings
                        key.SetValue("MaxConcurrentProcessing", MaxConcurrentProcessing.ToString());
                        key.SetValue("EnableAsyncProcessing", EnableAsyncProcessing.ToString());
                        key.SetValue("ProcessingTimeoutSeconds", ProcessingTimeoutSeconds.ToString());

                        // Save security settings
                        key.SetValue("ValidateFileSignatures", ValidateFileSignatures.ToString());
                        key.SetValue("BlockExecutableFiles", BlockExecutableFiles.ToString());

                        // Save list values
                        key.SetValue("ExcludedFileExtensions", string.Join(",", ExcludedFileExtensions));
                        key.SetValue("AllowedFileExtensions", string.Join(",", AllowedFileExtensions));
                        key.SetValue("ExcludedSenders", string.Join(",", ExcludedSenders));
                        key.SetValue("ExcludedDomains", string.Join(",", ExcludedDomains));
                        key.SetValue("BlockedFileTypes", string.Join(",", BlockedFileTypes));
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving configuration: {ex.Message}");
            }
        }

        private void LoadNumericValue<T>(string key, T defaultValue, Action<T> setter) where T : struct
        {
            try
            {
                var value = GetValue(key, defaultValue.ToString());
                if (typeof(T) == typeof(int) && int.TryParse(value, out var intValue))
                    setter((T)(object)intValue);
                else if (typeof(T) == typeof(long) && long.TryParse(value, out var longValue))
                    setter((T)(object)longValue);
                else if (typeof(T) == typeof(double) && double.TryParse(value, out var doubleValue))
                    setter((T)(object)doubleValue);
            }
            catch
            {
                setter(defaultValue);
            }
        }

        private bool LoadBooleanValue(string key, bool defaultValue)
        {
            try
            {
                return bool.TryParse(GetValue(key, defaultValue.ToString()), out var value) && value;
            }
            catch
            {
                return defaultValue;
            }
        }

        private void LoadListValue(string key, string defaultValue, List<string> targetList)
        {
            try
            {
                var value = GetValue(key, defaultValue);
                targetList.Clear();
                if (!string.IsNullOrEmpty(value))
                {
                    targetList.AddRange(value.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                        .Select(ext => ext.Trim().ToLowerInvariant())
                        .Where(ext => !string.IsNullOrEmpty(ext)));
                }
            }
            catch
            {
                targetList.Clear();
                if (!string.IsNullOrEmpty(defaultValue))
                {
                    targetList.AddRange(defaultValue.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                        .Select(ext => ext.Trim().ToLowerInvariant())
                        .Where(ext => !string.IsNullOrEmpty(ext)));
                }
            }
        }

        private void ValidateConfiguration()
        {
            // Validate numeric ranges
            ConnectionTimeoutSeconds = Math.Max(1, Math.Min(ConnectionTimeoutSeconds, 300));
            RetryCount = Math.Max(0, Math.Min(RetryCount, 10));
            RetryDelaySeconds = Math.Max(1, Math.Min(RetryDelaySeconds, 60));
            MaxRetryDelaySeconds = Math.Max(RetryDelaySeconds, Math.Min(MaxRetryDelaySeconds, 300));
            MaxFileSizeBytes = Math.Max(1024, Math.Min(MaxFileSizeBytes, 1024 * 1024 * 1024)); // 1KB to 1GB
            MaxAttachmentCount = Math.Max(1, Math.Min(MaxAttachmentCount, 1000));
            MaxEmailAgeDays = Math.Max(1, Math.Min(MaxEmailAgeDays, 365));
            MaxLogFileSizeMB = Math.Max(1, Math.Min(MaxLogFileSizeMB, 1000));
            MaxConcurrentProcessing = Math.Max(1, Math.Min(MaxConcurrentProcessing, 20));
            ProcessingTimeoutSeconds = Math.Max(10, Math.Min(ProcessingTimeoutSeconds, 3600));

            // Validate file paths
            if (string.IsNullOrEmpty(LogFilePath))
            {
                LogFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "EmailProcessor", "VSTO.log");
            }

            // Ensure log directory exists
            try
            {
                var logDirectory = Path.GetDirectoryName(LogFilePath);
                if (!string.IsNullOrEmpty(logDirectory) && !Directory.Exists(logDirectory))
                {
                    Directory.CreateDirectory(logDirectory);
                }
            }
            catch
            {
                // Use default if directory creation fails
                LogFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "EmailProcessor", "VSTO.log");
            }
        }

        public string GetValue(string key, string defaultValue = "")
        {
            try
            {
                using (var registryKey = Registry.CurrentUser.OpenSubKey(RegistryKeyPath))
                {
                    if (registryKey != null)
                    {
                        var value = registryKey.GetValue(key);
                        return value?.ToString() ?? defaultValue;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error reading registry value {key}: {ex.Message}");
            }
            return defaultValue;
        }

        public void SetValue(string key, string value)
        {
            try
            {
                using (var registryKey = Registry.CurrentUser.CreateSubKey(RegistryKeyPath))
                {
                    if (registryKey != null)
                    {
                        registryKey.SetValue(key, value);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error writing registry value {key}: {ex.Message}");
            }
        }

        // Phase 4: Additional utility methods
        public bool IsFileExtensionAllowed(string fileExtension)
        {
            if (string.IsNullOrEmpty(fileExtension))
                return false;

            var ext = fileExtension.ToLowerInvariant();
            
            // Check blocked file types first
            if (BlockedFileTypes.Contains(ext))
                return false;

            // Check excluded file extensions
            if (ExcludedFileExtensions.Contains(ext))
                return false;

            // If allowed extensions list is empty, all are allowed (except blocked/excluded)
            if (AllowedFileExtensions.Count == 0)
                return true;

            // Check if extension is in allowed list
            return AllowedFileExtensions.Contains(ext);
        }

        public bool IsSenderExcluded(string senderEmail)
        {
            if (string.IsNullOrEmpty(senderEmail))
                return false;

            var email = senderEmail.ToLowerInvariant();
            
            // Check excluded senders
            if (ExcludedSenders.Any(sender => email.Contains(sender.ToLowerInvariant())))
                return true;

            // Check excluded domains
            var domain = email.Split('@').LastOrDefault();
            if (!string.IsNullOrEmpty(domain))
            {
                return ExcludedDomains.Any(excludedDomain => domain.Contains(excludedDomain.ToLowerInvariant()));
            }

            return false;
        }

        public bool IsEmailTooOld(DateTime emailDate)
        {
            return emailDate < DateTime.Now.AddDays(-MaxEmailAgeDays);
        }
    }
} 