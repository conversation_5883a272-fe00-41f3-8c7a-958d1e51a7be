using System;
using System.Collections.Generic;
using EmailProcessor.Domain.ValueObjects;
using EmailProcessor.Domain.Events;

namespace EmailProcessor.Domain.Entities
{
    /// <summary>
    /// Core Email entity representing an email message in the domain
    /// </summary>
    public class Email
    {
        public long EmailId { get; private set; }
        public string Subject { get; private set; }
        public string SenderName { get; private set; }
        public EmailAddress SenderEmail { get; private set; }
        public List<EmailAddress> RecipientTo { get; private set; }
        public List<EmailAddress> RecipientCC { get; private set; }
        public EmailType EmailType { get; private set; }
        public DateTime Timestamp { get; private set; }
        public string OutlookMessageId { get; private set; }
        public ProcessingStatus ProcessingStatus { get; private set; }
        public DateTime CreatedDate { get; private set; }
        public DateTime ModifiedDate { get; private set; }
        
        // Navigation property for attachments
        public virtual ICollection<Attachment> Attachments { get; private set; }
        
        // Navigation property for processing logs
        public virtual ICollection<ProcessingLog> ProcessingLogs { get; private set; }

        // Private constructor for EF Core
        private Email() { }

        public Email(
            string subject,
            string senderName,
            EmailAddress senderEmail,
            List<EmailAddress> recipientTo,
            List<EmailAddress> recipientCC,
            EmailType emailType,
            DateTime timestamp,
            string outlookMessageId)
        {
            ValidateEmail(subject, senderEmail, emailType, timestamp, outlookMessageId);
            
            Subject = subject;
            SenderName = senderName;
            SenderEmail = senderEmail;
            RecipientTo = recipientTo ?? new List<EmailAddress>();
            RecipientCC = recipientCC ?? new List<EmailAddress>();
            EmailType = emailType;
            Timestamp = timestamp;
            OutlookMessageId = outlookMessageId;
            ProcessingStatus = ProcessingStatus.Pending;
            CreatedDate = DateTime.UtcNow;
            ModifiedDate = DateTime.UtcNow;
            
            Attachments = new List<Attachment>();
            ProcessingLogs = new List<ProcessingLog>();
            
            // Raise domain event
            AddDomainEvent(new EmailCreatedEvent(this));
        }

        public void UpdateProcessingStatus(ProcessingStatus newStatus)
        {
            if (ProcessingStatus == newStatus)
                return;
                
            ProcessingStatus = newStatus;
            ModifiedDate = DateTime.UtcNow;
            
            AddDomainEvent(new EmailProcessingStatusChangedEvent(this, newStatus));
        }

        public void AddAttachment(Attachment attachment)
        {
            if (attachment == null)
                throw new ArgumentNullException(nameof(attachment));
                
            Attachments.Add(attachment);
            ModifiedDate = DateTime.UtcNow;
        }

        public void AddProcessingLog(ProcessingLog log)
        {
            if (log == null)
                throw new ArgumentNullException(nameof(log));
                
            ProcessingLogs.Add(log);
        }

        private void ValidateEmail(string subject, EmailAddress senderEmail, EmailType emailType, DateTime timestamp, string outlookMessageId)
        {
            if (string.IsNullOrWhiteSpace(subject))
                throw new ArgumentException("Email subject cannot be null or empty", nameof(subject));
                
            if (senderEmail == null)
                throw new ArgumentException("Sender email cannot be null", nameof(senderEmail));
                
            if (timestamp == default)
                throw new ArgumentException("Timestamp must be a valid date", nameof(timestamp));
                
            if (string.IsNullOrWhiteSpace(outlookMessageId))
                throw new ArgumentException("Outlook message ID cannot be null or empty", nameof(outlookMessageId));
        }

        // Domain events
        private readonly List<IDomainEvent> _domainEvents = new List<IDomainEvent>();
        public IReadOnlyCollection<IDomainEvent> DomainEvents => _domainEvents.AsReadOnly();

        protected void AddDomainEvent(IDomainEvent domainEvent)
        {
            _domainEvents.Add(domainEvent);
        }

        public void ClearDomainEvents()
        {
            _domainEvents.Clear();
        }
    }
} 