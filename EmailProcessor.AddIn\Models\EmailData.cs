using System;
using System.Collections.Generic;

namespace EmailProcessor.AddIn.Models
{
    /// <summary>
    /// Enhanced email data extracted from Outlook MailItem
    /// Phase 4: Added additional metadata fields for comprehensive email processing
    /// </summary>
    public class EmailData
    {
        public string Subject { get; set; } = string.Empty;
        public string SenderName { get; set; } = string.Empty;
        public string SenderEmail { get; set; } = string.Empty;
        public List<string> RecipientTo { get; set; } = new List<string>();
        public List<string> RecipientCC { get; set; } = new List<string>();
        public List<string> RecipientBCC { get; set; } = new List<string>(); // Phase 4: Added BCC support
        public string EmailType { get; set; } = "Received"; // "Received" or "Sent"
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public string OutlookMessageId { get; set; } = string.Empty;
        public string EntryId { get; set; } = string.Empty;
        public string ConversationId { get; set; } = string.Empty;
        public bool HasAttachments { get; set; }
        public int AttachmentCount { get; set; }
        
        // Phase 4: Additional metadata fields
        public long MessageSize { get; set; } // Size in bytes
        public bool IsRead { get; set; } = true;
        public string Importance { get; set; } = "Normal"; // "Low", "Normal", "High"
        public string Categories { get; set; } = string.Empty;
        public string Sensitivity { get; set; } = "Normal"; // "Normal", "Personal", "Private", "Confidential"
    }
} 