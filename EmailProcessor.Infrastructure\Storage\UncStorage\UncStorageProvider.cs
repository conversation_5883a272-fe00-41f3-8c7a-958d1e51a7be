using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using System.Net;
using System.ComponentModel;
using System.Runtime.InteropServices;
using EmailProcessor.Domain.Interfaces;
using EmailProcessor.Domain.ValueObjects;
using EmailProcessor.Infrastructure.Logging;

namespace EmailProcessor.Infrastructure.Storage.UncStorage
{
    public class UncStorageProvider : IStorageProvider
    {
        private readonly string _uncPath;
        private readonly string _username;
        private readonly string _password;
        private readonly ILoggingProvider _loggingProvider;
        private readonly int _maxRetries;
        private readonly int _retryDelayMs;
        private readonly int _networkTimeoutMs;

        // Windows API for network connection management
        [DllImport("mpr.dll")]
        private static extern int WNetAddConnection2(NetResource netResource, string password, string username, int flags);

        [DllImport("mpr.dll")]
        private static extern int WNetCancelConnection2(string name, int flags, bool force);

        [StructLayout(LayoutKind.Sequential)]
        private class NetResource
        {
            public int Scope = 0;
            public int Type = 0;
            public int DisplayType = 0;
            public int Usage = 0;
            public string LocalName = "";
            public string RemoteName = "";
            public string Comment = "";
            public string Provider = "";
        }

        public UncStorageProvider(string uncPath, ILoggingProvider loggingProvider = null, string username = null, string password = null, 
                                int maxRetries = 3, int retryDelayMs = 2000, int networkTimeoutMs = 30000)
        {
            if (string.IsNullOrWhiteSpace(uncPath))
                throw new ArgumentException("UncPath cannot be null or empty", nameof(uncPath));

            if (!uncPath.StartsWith(@"\\"))
                throw new ArgumentException("UncPath must be a valid UNC path starting with \\\\", nameof(uncPath));

            _uncPath = uncPath.TrimEnd('\\');
            _username = username;
            _password = password;
            _loggingProvider = loggingProvider;
            _maxRetries = Math.Max(1, maxRetries);
            _retryDelayMs = Math.Max(1000, retryDelayMs);
            _networkTimeoutMs = Math.Max(5000, networkTimeoutMs);

            _loggingProvider?.LogInformation($"Initializing UNC storage provider: {_uncPath}");

            // Test connectivity on initialization (with retry)
            var connected = ExecuteWithRetryAsync(async () =>
            {
                var result = await TestConnectivityAsync();
                if (!result)
                    throw new InvalidOperationException($"Cannot connect to UNC path: {_uncPath}");
                return result;
            }, "Initial connectivity test").Result;

            if (!connected)
            {
                throw new InvalidOperationException($"Cannot establish connection to UNC path: {_uncPath}");
            }

            _loggingProvider?.LogInformation($"Successfully connected to UNC storage: {_uncPath}");
        }

        public StorageType StorageType => StorageType.Unc;

        public string BasePath => _uncPath;

        public async Task<FilePath> SaveFileAsync(byte[] fileData, string fileName, string directoryPath)
        {
            if (fileData == null)
                throw new ArgumentNullException(nameof(fileData));
            if (string.IsNullOrWhiteSpace(fileName))
                throw new ArgumentException("FileName cannot be null or empty", nameof(fileName));

            var fullPath = Path.Combine(_uncPath, directoryPath ?? "", fileName);
            var directory = Path.GetDirectoryName(fullPath);

            return await ExecuteWithRetryAsync(async () =>
            {
                try
                {
                    // Ensure network connection is active
                    await EnsureNetworkConnectionAsync();

                    // Create directory if it doesn't exist
                    if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                    {
                        Directory.CreateDirectory(directory);
                        _loggingProvider?.LogDebug($"Created UNC directory: {directory}");
                    }

                    // Validate available space (basic check for UNC)
                    try
                    {
                        var availableSpace = await GetAvailableSpaceAsync();
                        if (availableSpace < fileData.Length * 2) // Need at least 2x file size for safety
                        {
                            throw new InvalidOperationException($"Insufficient UNC storage space. Available: {availableSpace} bytes, Required: {fileData.Length * 2} bytes");
                        }
                    }
                    catch (Exception ex)
                    {
                        _loggingProvider?.LogWarning($"Could not determine UNC available space: {ex.Message}");
                        // Continue without space check - let the write operation fail naturally if no space
                    }

                    // Write file atomically using temp file
                    var tempPath = fullPath + ".tmp";
                    await File.WriteAllBytesAsync(tempPath, fileData);
                    
                    // Verify file was written correctly
                    var tempFileInfo = new FileInfo(tempPath);
                    if (tempFileInfo.Length != fileData.Length)
                    {
                        File.Delete(tempPath);
                        throw new InvalidOperationException($"UNC file size mismatch after write. Expected: {fileData.Length}, Actual: {tempFileInfo.Length}");
                    }

                    // Move temp file to final location
                    if (File.Exists(fullPath))
                    {
                        File.Delete(fullPath);
                    }
                    File.Move(tempPath, fullPath);

                    _loggingProvider?.LogDebug($"Successfully saved file to UNC storage: {fullPath}");
                    return FilePath.Create(fullPath);
                }
                catch (Exception ex)
                {
                    _loggingProvider?.LogError($"Error saving file to UNC storage: {fullPath}", ex);
                    
                    // Clean up temp file if it exists
                    var tempPath = fullPath + ".tmp";
                    if (File.Exists(tempPath))
                    {
                        try { File.Delete(tempPath); } catch { }
                    }
                    
                    throw;
                }
            }, $"SaveFileAsync (UNC): {fileName}");
        }

        public async Task<FilePath> SaveFileAsync(Stream fileStream, string fileName, string directoryPath)
        {
            if (fileStream == null)
                throw new ArgumentNullException(nameof(fileStream));
            if (string.IsNullOrWhiteSpace(fileName))
                throw new ArgumentException("FileName cannot be null or empty", nameof(fileName));

            var fullPath = Path.Combine(_uncPath, directoryPath ?? "", fileName);
            var directory = Path.GetDirectoryName(fullPath);

            return await ExecuteWithRetryAsync(async () =>
            {
                try
                {
                    // Ensure network connection is active
                    await EnsureNetworkConnectionAsync();

                    // Create directory if it doesn't exist
                    if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                    {
                        Directory.CreateDirectory(directory);
                        _loggingProvider?.LogDebug($"Created UNC directory: {directory}");
                    }

                    // Write file atomically using temp file
                    var tempPath = fullPath + ".tmp";
                    long bytesWritten = 0;

                    using (var tempFileStream = File.Create(tempPath))
                    {
                        var originalPosition = fileStream.Position;
                        fileStream.Position = 0; // Ensure we start from the beginning
                        
                        await fileStream.CopyToAsync(tempFileStream);
                        bytesWritten = tempFileStream.Length;
                        
                        fileStream.Position = originalPosition; // Restore original position
                    }

                    // Verify file was written
                    var tempFileInfo = new FileInfo(tempPath);
                    if (!tempFileInfo.Exists || tempFileInfo.Length == 0)
                    {
                        File.Delete(tempPath);
                        throw new InvalidOperationException($"Failed to write UNC file or file is empty: {tempPath}");
                    }

                    // Move temp file to final location
                    if (File.Exists(fullPath))
                    {
                        File.Delete(fullPath);
                    }
                    File.Move(tempPath, fullPath);

                    _loggingProvider?.LogDebug($"Successfully saved file from stream to UNC storage: {fullPath}, Size: {bytesWritten} bytes");
                    return FilePath.Create(fullPath);
                }
                catch (Exception ex)
                {
                    _loggingProvider?.LogError($"Error saving file from stream to UNC storage: {fullPath}", ex);
                    
                    // Clean up temp file if it exists
                    var tempPath = fullPath + ".tmp";
                    if (File.Exists(tempPath))
                    {
                        try { File.Delete(tempPath); } catch { }
                    }
                    
                    throw;
                }
            }, $"SaveFileAsync (UNC Stream): {fileName}");
        }

        public async Task CreateDirectoryAsync(string directoryPath)
        {
            var fullPath = Path.Combine(_uncPath, directoryPath);
            if (!Directory.Exists(fullPath))
            {
                Directory.CreateDirectory(fullPath);
            }
            await Task.CompletedTask;
        }

        public async Task<bool> FileExistsAsync(string filePath)
        {
            var exists = File.Exists(filePath);
            await Task.CompletedTask;
            return exists;
        }

        public async Task<bool> DirectoryExistsAsync(string directoryPath)
        {
            var fullPath = Path.Combine(_uncPath, directoryPath);
            var exists = Directory.Exists(fullPath);
            await Task.CompletedTask;
            return exists;
        }

        public async Task DeleteFileAsync(string filePath)
        {
            if (File.Exists(filePath))
            {
                File.Delete(filePath);
            }
            await Task.CompletedTask;
        }

        public async Task<FileInfo> GetFileInfoAsync(string filePath)
        {
            var fileInfo = new FileInfo(filePath);
            await Task.CompletedTask;
            return fileInfo;
        }

        public async Task<long> GetAvailableSpaceAsync()
        {
            var driveInfo = new DriveInfo(_uncPath);
            await Task.CompletedTask;
            return driveInfo.AvailableFreeSpace;
        }

        public async Task<bool> TestConnectivityAsync()
        {
            try
            {
                // Ensure network connection first
                await EnsureNetworkConnectionAsync();

                // Test if we can access the UNC path
                if (!Directory.Exists(_uncPath))
                {
                    _loggingProvider?.LogDebug($"UNC path does not exist: {_uncPath}");
                    return false;
                }

                // Try to create a test file
                var testFile = Path.Combine(_uncPath, $"test_{Guid.NewGuid()}.tmp");
                await File.WriteAllTextAsync(testFile, "test");
                
                // Verify file was created
                if (!File.Exists(testFile))
                {
                    _loggingProvider?.LogDebug($"UNC test file creation failed: {testFile}");
                    return false;
                }

                // Clean up test file
                File.Delete(testFile);
                _loggingProvider?.LogDebug($"UNC connectivity test successful: {_uncPath}");
                return true;
            }
            catch (Exception ex)
            {
                _loggingProvider?.LogDebug($"UNC connectivity test failed: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Ensures network connection to UNC path is established
        /// </summary>
        private async Task EnsureNetworkConnectionAsync()
        {
            if (string.IsNullOrEmpty(_username) || string.IsNullOrEmpty(_password))
            {
                // No credentials provided, assume current user context
                return;
            }

            try
            {
                var netResource = new NetResource
                {
                    RemoteName = _uncPath,
                    Type = 1 // RESOURCETYPE_DISK
                };

                var result = WNetAddConnection2(netResource, _password, _username, 0);
                
                if (result != 0 && result != 1219) // 1219 = Already connected
                {
                    var ex = new Win32Exception(result);
                    _loggingProvider?.LogWarning($"Failed to establish UNC connection: {ex.Message} (Code: {result})");
                    throw new InvalidOperationException($"Cannot connect to UNC path {_uncPath}: {ex.Message}");
                }

                _loggingProvider?.LogDebug($"UNC network connection established or already active: {_uncPath}");
            }
            catch (Exception ex)
            {
                _loggingProvider?.LogError($"Error establishing UNC network connection: {ex.Message}", ex);
                throw;
            }

            await Task.CompletedTask;
        }

        /// <summary>
        /// Executes an operation with retry logic for network-related transient errors
        /// </summary>
        private async Task<T> ExecuteWithRetryAsync<T>(Func<Task<T>> operation, string operationName)
        {
            var attempt = 1;
            Exception lastException = null;

            while (attempt <= _maxRetries)
            {
                try
                {
                    return await operation();
                }
                catch (Exception ex) when (IsNetworkTransientError(ex) && attempt < _maxRetries)
                {
                    lastException = ex;
                    _loggingProvider?.LogWarning($"Network transient error in {operationName} (attempt {attempt}/{_maxRetries}): {ex.Message}");
                    
                    // Exponential backoff with jitter for network operations
                    var delay = _retryDelayMs * (int)Math.Pow(2, attempt - 1);
                    var jitter = new Random().Next(0, delay / 3); // Add up to 33% jitter for network operations
                    await Task.Delay(delay + jitter);
                    
                    attempt++;
                }
                catch (Exception ex)
                {
                    _loggingProvider?.LogError($"Non-transient error in {operationName}: {ex.Message}", ex);
                    throw;
                }
            }

            _loggingProvider?.LogError($"Network operation {operationName} failed after {_maxRetries} attempts", lastException);
            throw lastException ?? new InvalidOperationException($"Network operation {operationName} failed after {_maxRetries} attempts");
        }

        /// <summary>
        /// Determines if an exception represents a network-related transient error that should be retried
        /// </summary>
        private static bool IsNetworkTransientError(Exception ex)
        {
            return ex is IOException ||
                   ex is UnauthorizedAccessException ||
                   ex is DirectoryNotFoundException ||
                   ex is DriveNotFoundException ||
                   ex is TimeoutException ||
                   (ex is SystemException && (
                       ex.Message.Contains("network path was not found") ||
                       ex.Message.Contains("being used by another process") ||
                       ex.Message.Contains("network name cannot be found") ||
                       ex.Message.Contains("specified network resource or device is no longer available") ||
                       ex.Message.Contains("network is busy") ||
                       ex.Message.Contains("network request is not supported"))) ||
                   (ex is Win32Exception w32ex && (
                       w32ex.NativeErrorCode == 53 ||  // Network path not found
                       w32ex.NativeErrorCode == 67 ||  // Network name not found  
                       w32ex.NativeErrorCode == 121 || // Semaphore timeout
                       w32ex.NativeErrorCode == 1326   // Logon failure
                   ));
        }
    }
} 