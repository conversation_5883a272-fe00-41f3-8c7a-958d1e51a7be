using System;
using EmailProcessor.Domain.Entities;

namespace EmailProcessor.Domain.Events
{
    /// <summary>
    /// Domain event raised when a new email is created
    /// </summary>
    public class EmailCreatedEvent : IDomainEvent
    {
        public Email Email { get; }
        public DateTime OccurredOn { get; }

        public EmailCreatedEvent(Email email)
        {
            Email = email ?? throw new ArgumentNullException(nameof(email));
            OccurredOn = DateTime.UtcNow;
        }
    }
} 