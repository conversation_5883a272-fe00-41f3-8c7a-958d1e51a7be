using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using EmailProcessor.Domain.ValueObjects;

namespace EmailProcessor.Domain.Services
{
    /// <summary>
    /// Domain service for validation logic
    /// </summary>
    public class ValidationService
    {
        /// <summary>
        /// Validates email address format
        /// </summary>
        public bool IsValidEmailAddress(string emailAddress)
        {
            if (string.IsNullOrWhiteSpace(emailAddress))
                return false;

            try
            {
                // Basic email validation regex
                var emailRegex = new Regex(@"^[^@\s]+@[^@\s]+\.[^@\s]+$");
                return emailRegex.IsMatch(emailAddress);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Validates file path format
        /// </summary>
        public bool IsValidFilePath(string filePath)
        {
            if (string.IsNullOrWhiteSpace(filePath))
                return false;

            try
            {
                // Check if the path contains invalid characters
                var invalidChars = System.IO.Path.GetInvalidPathChars();
                if (filePath.IndexOfAny(invalidChars) >= 0)
                    return false;

                // Check if it's a valid path format
                System.IO.Path.GetFullPath(filePath);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Validates file extension
        /// </summary>
        public bool IsValidFileExtension(string fileExtension, IEnumerable<string> allowedExtensions = null)
        {
            if (string.IsNullOrWhiteSpace(fileExtension))
                return false;

            // Remove leading dot if present
            var cleanExtension = fileExtension.StartsWith(".") ? fileExtension.Substring(1) : fileExtension;

            // Check for invalid characters
            var invalidChars = System.IO.Path.GetInvalidFileNameChars();
            if (cleanExtension.IndexOfAny(invalidChars) >= 0)
                return false;

            // If allowed extensions are specified, check against them
            if (allowedExtensions != null && allowedExtensions.Any())
            {
                return allowedExtensions.Any(ext => 
                    string.Equals(ext.TrimStart('.'), cleanExtension, StringComparison.OrdinalIgnoreCase));
            }

            return true;
        }

        /// <summary>
        /// Validates file size
        /// </summary>
        public bool IsValidFileSize(long fileSize, long maxFileSize)
        {
            return fileSize >= 0 && fileSize <= maxFileSize;
        }

        /// <summary>
        /// Validates email subject
        /// </summary>
        public bool IsValidEmailSubject(string subject)
        {
            if (string.IsNullOrWhiteSpace(subject))
                return false;

            // Check for reasonable length (most email clients support up to 998 characters)
            if (subject.Length > 998)
                return false;

            return true;
        }

        /// <summary>
        /// Validates email sender name
        /// </summary>
        public bool IsValidSenderName(string senderName)
        {
            // Sender name can be null or empty (optional field)
            if (string.IsNullOrWhiteSpace(senderName))
                return true;

            // Check for reasonable length
            if (senderName.Length > 255)
                return false;

            return true;
        }

        /// <summary>
        /// Validates Outlook message ID
        /// </summary>
        public bool IsValidOutlookMessageId(string outlookMessageId)
        {
            if (string.IsNullOrWhiteSpace(outlookMessageId))
                return false;

            // Check for reasonable length
            if (outlookMessageId.Length > 255)
                return false;

            return true;
        }

        /// <summary>
        /// Validates timestamp
        /// </summary>
        public bool IsValidTimestamp(DateTime timestamp)
        {
            // Check if timestamp is not default and not in the future
            if (timestamp == default)
                return false;

            if (timestamp > DateTime.UtcNow.AddMinutes(5)) // Allow 5 minutes clock skew
                return false;

            return true;
        }

        /// <summary>
        /// Validates email type
        /// </summary>
        public bool IsValidEmailType(EmailType emailType)
        {
            return Enum.IsDefined(typeof(EmailType), emailType);
        }

        /// <summary>
        /// Validates processing status
        /// </summary>
        public bool IsValidProcessingStatus(ProcessingStatus status)
        {
            return Enum.IsDefined(typeof(ProcessingStatus), status);
        }

        /// <summary>
        /// Validates log level
        /// </summary>
        public bool IsValidLogLevel(LogLevel logLevel)
        {
            return Enum.IsDefined(typeof(LogLevel), logLevel);
        }

        /// <summary>
        /// Validates correlation ID
        /// </summary>
        public bool IsValidCorrelationId(Guid? correlationId)
        {
            // Correlation ID can be null
            if (!correlationId.HasValue)
                return true;

            return correlationId.Value != Guid.Empty;
        }

        /// <summary>
        /// Validates source component name
        /// </summary>
        public bool IsValidSourceComponent(string sourceComponent)
        {
            if (string.IsNullOrWhiteSpace(sourceComponent))
                return false;

            // Check for reasonable length
            if (sourceComponent.Length > 100)
                return false;

            // Check for invalid characters
            var invalidChars = new[] { '<', '>', ':', '"', '/', '\\', '|', '?', '*' };
            if (sourceComponent.IndexOfAny(invalidChars) >= 0)
                return false;

            return true;
        }

        /// <summary>
        /// Validates log message
        /// </summary>
        public bool IsValidLogMessage(string message)
        {
            if (string.IsNullOrWhiteSpace(message))
                return false;

            // Check for reasonable length
            if (message.Length > 10000) // 10KB limit for log messages
                return false;

            return true;
        }

        /// <summary>
        /// Validates directory path
        /// </summary>
        public bool IsValidDirectoryPath(string directoryPath)
        {
            if (string.IsNullOrWhiteSpace(directoryPath))
                return false;

            try
            {
                // Check if the path contains invalid characters
                var invalidChars = System.IO.Path.GetInvalidPathChars();
                if (directoryPath.IndexOfAny(invalidChars) >= 0)
                    return false;

                // Check if it's a valid path format
                System.IO.Path.GetFullPath(directoryPath);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Validates UNC path
        /// </summary>
        public bool IsValidUncPath(string uncPath)
        {
            if (string.IsNullOrWhiteSpace(uncPath))
                return false;

            // Check if it starts with \\
            if (!uncPath.StartsWith(@"\\"))
                return false;

            // Validate the rest as a regular path
            return IsValidDirectoryPath(uncPath);
        }

        /// <summary>
        /// Validates local path
        /// </summary>
        public bool IsValidLocalPath(string localPath)
        {
            if (string.IsNullOrWhiteSpace(localPath))
                return false;

            // Check if it doesn't start with \\ (not UNC)
            if (localPath.StartsWith(@"\\"))
                return false;

            // Validate as a regular path
            return IsValidDirectoryPath(localPath);
        }
    }
} 