using System;
using System.IO;
using System.Collections.Generic;

namespace EmailProcessor.Domain.ValueObjects
{
    /// <summary>
    /// Value object representing a file path with validation
    /// </summary>
    public class FilePath : IEquatable<FilePath>
    {
        public string Value { get; }

        private FilePath(string value)
        {
            Value = value;
        }

        public static FilePath Create(string filePath)
        {
            if (string.IsNullOrWhiteSpace(filePath))
                throw new ArgumentException("File path cannot be null or empty", nameof(filePath));

            if (!IsValidPath(filePath))
                throw new ArgumentException("Invalid file path format", nameof(filePath));

            return new FilePath(Path.GetFullPath(filePath));
        }

        public static bool IsValidPath(string filePath)
        {
            if (string.IsNullOrWhiteSpace(filePath))
                return false;

            try
            {
                // Check if the path contains invalid characters
                var invalidChars = Path.GetInvalidPathChars();
                if (filePath.IndexOfAny(invalidChars) >= 0)
                    return false;

                // Check if it's a valid path format
                Path.GetFullPath(filePath);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public string GetFileName()
        {
            return Path.GetFileName(Value);
        }

        public string GetDirectoryName()
        {
            return Path.GetDirectoryName(Value);
        }

        public string GetExtension()
        {
            return Path.GetExtension(Value);
        }

        public bool IsUncPath()
        {
            return Value.StartsWith(@"\\");
        }

        public bool IsLocalPath()
        {
            return !IsUncPath() && Path.IsPathRooted(Value);
        }

        public override string ToString()
        {
            return Value;
        }

        public override bool Equals(object obj)
        {
            return Equals(obj as FilePath);
        }

        public bool Equals(FilePath other)
        {
            if (other is null) return false;
            if (ReferenceEquals(this, other)) return true;
            return string.Equals(Value, other.Value, StringComparison.OrdinalIgnoreCase);
        }

        public override int GetHashCode()
        {
            return Value?.ToLowerInvariant().GetHashCode() ?? 0;
        }

        public static bool operator ==(FilePath left, FilePath right)
        {
            return EqualityComparer<FilePath>.Default.Equals(left, right);
        }

        public static bool operator !=(FilePath left, FilePath right)
        {
            return !(left == right);
        }

        public static implicit operator string(FilePath filePath)
        {
            return filePath?.Value;
        }
    }
} 