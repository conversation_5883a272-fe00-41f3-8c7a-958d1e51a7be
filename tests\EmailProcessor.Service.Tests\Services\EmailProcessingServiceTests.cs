using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using EmailProcessor.Domain.Entities;
using EmailProcessor.Domain.Interfaces;
using EmailProcessor.Domain.ValueObjects;
using EmailProcessor.Infrastructure.Storage.DualStorageService;
using EmailProcessor.Infrastructure.Storage.LocalStorage;
using EmailProcessor.Infrastructure.Storage.UncStorage;
using EmailProcessor.Infrastructure.Logging;
using EmailProcessor.Service.Services;
using EmailProcessor.Shared.Models;
using FluentAssertions;
using Moq;
using Xunit;

namespace EmailProcessor.Service.Tests.Services
{
    public class EmailProcessingServiceTests
    {
        private readonly Mock<IEmailRepository> _mockEmailRepository;
        private readonly Mock<IAttachmentRepository> _mockAttachmentRepository;
        private readonly Mock<IProcessingLogRepository> _mockProcessingLogRepository;
        private readonly Mock<ILoggingProvider> _mockLoggingProvider;
        private readonly EmailProcessingService _emailProcessingService;

        public EmailProcessingServiceTests()
        {
            _mockEmailRepository = new Mock<IEmailRepository>();
            _mockAttachmentRepository = new Mock<IAttachmentRepository>();
            _mockProcessingLogRepository = new Mock<IProcessingLogRepository>();
            _mockLoggingProvider = new Mock<ILoggingProvider>();

            // Create mock storage providers to avoid UNC connection issues in tests
            var mockLocalStorageProvider = new Mock<IStorageProvider>();
            var mockUncStorageProvider = new Mock<IStorageProvider>();

            // Setup mock storage providers
            mockLocalStorageProvider.Setup(x => x.StorageType).Returns(StorageType.Local);
            mockLocalStorageProvider.Setup(x => x.BasePath).Returns("C:\\TestStorage");
            mockUncStorageProvider.Setup(x => x.StorageType).Returns(StorageType.Unc);
            mockUncStorageProvider.Setup(x => x.BasePath).Returns("\\\\test\\share");

            // Create real DualStorageService with mocked providers
            var dualStorageService = new DualStorageService(
                mockLocalStorageProvider.Object,
                mockUncStorageProvider.Object,
                _mockLoggingProvider.Object);

            var attachmentHandlerService = new AttachmentHandlerService(
                _mockAttachmentRepository.Object,
                _mockProcessingLogRepository.Object,
                _mockLoggingProvider.Object,
                dualStorageService);

            _emailProcessingService = new EmailProcessingService(
                _mockEmailRepository.Object,
                _mockAttachmentRepository.Object,
                _mockProcessingLogRepository.Object,
                _mockLoggingProvider.Object,
                attachmentHandlerService,
                dualStorageService);
        }

        [Fact]
        public async Task ProcessEmailAsync_WithValidRequest_ShouldProcessSuccessfully()
        {
            // Arrange
            var request = CreateValidEmailProcessingRequest();
            var email = CreateTestEmail();
            var attachment = CreateTestAttachment(email.EmailId);

            _mockEmailRepository.Setup(x => x.GetByOutlookMessageIdAsync(It.IsAny<string>()))
                .ReturnsAsync((Email?)null);
            _mockEmailRepository.Setup(x => x.AddAsync(It.IsAny<Email>()))
                .ReturnsAsync(email);
            _mockEmailRepository.Setup(x => x.UpdateAsync(It.IsAny<Email>()))
                .ReturnsAsync(email);

            _mockAttachmentRepository.Setup(x => x.AddAsync(It.IsAny<Attachment>()))
                .ReturnsAsync(attachment);
            _mockAttachmentRepository.Setup(x => x.UpdateAsync(It.IsAny<Attachment>()))
                .ReturnsAsync(attachment);

            // Act
            var result = await _emailProcessingService.ProcessEmailAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.EmailId.Should().Be(email.EmailId);
            result.ProcessingResults.Should().HaveCount(1);

            _mockEmailRepository.Verify(x => x.AddAsync(It.IsAny<Email>()), Times.Once);
            _mockEmailRepository.Verify(x => x.UpdateAsync(It.IsAny<Email>()), Times.Once);
            _mockAttachmentRepository.Verify(x => x.AddAsync(It.IsAny<Attachment>()), Times.Once);
            _mockAttachmentRepository.Verify(x => x.UpdateAsync(It.IsAny<Attachment>()), Times.Once);
        }

        [Fact]
        public async Task ProcessEmailAsync_WithDuplicateEmail_ShouldReturnFailure()
        {
            // Arrange
            var request = CreateValidEmailProcessingRequest();
            var existingEmail = CreateTestEmail();

            _mockEmailRepository.Setup(x => x.GetByOutlookMessageIdAsync(It.IsAny<string>()))
                .ReturnsAsync(existingEmail);

            // Act
            var result = await _emailProcessingService.ProcessEmailAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeFalse();
            result.EmailId.Should().Be(existingEmail.EmailId);
            result.Message.Should().Be("Email already processed");

            _mockEmailRepository.Verify(x => x.AddAsync(It.IsAny<Email>()), Times.Never);
        }

        [Fact]
        public async Task ProcessEmailAsync_WithInvalidRequest_ShouldReturnFailure()
        {
            // Arrange
            var request = CreateInvalidEmailProcessingRequest();

            // Act
            var result = await _emailProcessingService.ProcessEmailAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeFalse();
            result.Message.Should().Be("Invalid request data");

            _mockEmailRepository.Verify(x => x.AddAsync(It.IsAny<Email>()), Times.Never);
        }

        [Fact]
        public async Task ProcessEmailAsync_WithException_ShouldReturnFailure()
        {
            // Arrange
            var request = CreateValidEmailProcessingRequest();

            _mockEmailRepository.Setup(x => x.GetByOutlookMessageIdAsync(It.IsAny<string>()))
                .ThrowsAsync(new Exception("Database connection failed"));

            // Act
            var result = await _emailProcessingService.ProcessEmailAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeFalse();
            result.Message.Should().Be("Email processing failed");
            result.ErrorDetails.Should().Be("Database connection failed");
        }

        private EmailProcessingRequest CreateValidEmailProcessingRequest()
        {
            return new EmailProcessingRequest
            {
                CorrelationId = Guid.NewGuid(),
                Timestamp = DateTime.UtcNow,
                Data = new EmailProcessingData
                {
                    Email = new EmailData
                    {
                        Subject = "Test Email",
                        SenderName = "Test Sender",
                        SenderEmail = "<EMAIL>",
                        RecipientTo = new List<string> { "<EMAIL>" },
                        RecipientCC = new List<string>(),
                        EmailType = "Received",
                        Timestamp = DateTime.UtcNow,
                        OutlookMessageId = "test-message-id-123"
                    },
                    Attachments = new List<AttachmentData>
                    {
                        new AttachmentData
                        {
                            FileName = "test.pdf",
                            OriginalFileName = "test.pdf",
                            ContentType = "application/pdf",
                            FileExtension = "pdf",
                            FileSize = 1024,
                            FileData = Convert.ToBase64String(new byte[1024])
                        }
                    }
                }
            };
        }

        private EmailProcessingRequest CreateInvalidEmailProcessingRequest()
        {
            return new EmailProcessingRequest
            {
                CorrelationId = Guid.NewGuid(),
                Timestamp = DateTime.UtcNow,
                Data = new EmailProcessingData
                {
                    Email = new EmailData
                    {
                        Subject = "", // Invalid - empty subject
                        SenderName = "Test Sender",
                        SenderEmail = "<EMAIL>",
                        RecipientTo = new List<string> { "<EMAIL>" },
                        RecipientCC = new List<string>(),
                        EmailType = "Received",
                        Timestamp = DateTime.UtcNow,
                        OutlookMessageId = "" // Invalid - empty message ID
                    },
                    Attachments = new List<AttachmentData>()
                }
            };
        }

        private Email CreateTestEmail()
        {
            var senderEmail = EmailAddress.Create("<EMAIL>");
            var recipientTo = new List<EmailAddress> { EmailAddress.Create("<EMAIL>") };
            var recipientCC = new List<EmailAddress>();

            var email = new Email(
                "Test Email",
                "Test Sender",
                senderEmail,
                recipientTo,
                recipientCC,
                EmailType.Received,
                DateTime.UtcNow,
                "test-message-id-123");

            // Set a valid EmailId for testing (simulating database assignment)
            typeof(Email).GetProperty("EmailId")?.SetValue(email, 1L);

            return email;
        }

        private Attachment CreateTestAttachment(long emailId)
        {
            return new Attachment(
                emailId,
                "test.pdf",
                "test.pdf",
                "application/pdf",
                "pdf",
                1024);
        }
    }
} 