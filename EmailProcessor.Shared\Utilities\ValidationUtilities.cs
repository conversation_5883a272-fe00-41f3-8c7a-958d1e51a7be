using EmailProcessor.Shared.Constants;

namespace EmailProcessor.Shared.Utilities
{
    /// <summary>
    /// Shared validation utilities for email processing
    /// </summary>
    public static class ValidationUtilities
    {
        /// <summary>
        /// Validates if a file extension is allowed
        /// </summary>
        /// <param name="fileExtension">The file extension to validate</param>
        /// <returns>True if the extension is allowed, false otherwise</returns>
        public static bool IsFileExtensionAllowed(string fileExtension)
        {
            if (string.IsNullOrWhiteSpace(fileExtension))
                return false;

            var extension = fileExtension.ToLowerInvariant();
            
            // Check if extension is explicitly excluded
            if (ProcessingConstants.ExcludedFileExtensions.Contains(extension))
                return false;

            // If allowed extensions are specified, check if extension is in the list
            if (ProcessingConstants.AllowedFileExtensions.Length > 0)
            {
                return ProcessingConstants.AllowedFileExtensions.Contains(extension);
            }

            // If no allowed extensions specified, allow all except excluded ones
            return true;
        }

        /// <summary>
        /// Validates if a file size is within acceptable limits
        /// </summary>
        /// <param name="fileSize">The file size in bytes</param>
        /// <returns>True if the file size is acceptable, false otherwise</returns>
        public static bool IsFileSizeAcceptable(long fileSize)
        {
            return fileSize > 0 && fileSize <= ProcessingConstants.MaxFileSizeBytes;
        }

        /// <summary>
        /// Validates email address format
        /// </summary>
        /// <param name="email">The email address to validate</param>
        /// <returns>True if the email format is valid, false otherwise</returns>
        public static bool IsValidEmailAddress(string email)
        {
            if (string.IsNullOrWhiteSpace(email))
                return false;

            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Validates if a string is a valid correlation ID
        /// </summary>
        /// <param name="correlationId">The correlation ID to validate</param>
        /// <returns>True if the correlation ID is valid, false otherwise</returns>
        public static bool IsValidCorrelationId(string correlationId)
        {
            return Guid.TryParse(correlationId, out _);
        }

        /// <summary>
        /// Validates if a message type is supported
        /// </summary>
        /// <param name="messageType">The message type to validate</param>
        /// <returns>True if the message type is supported, false otherwise</returns>
        public static bool IsSupportedMessageType(string messageType)
        {
            if (string.IsNullOrWhiteSpace(messageType))
                return false;

            return messageType switch
            {
                ProcessingConstants.MessageTypes.EmailProcessingRequest => true,
                ProcessingConstants.MessageTypes.EmailProcessingResponse => true,
                ProcessingConstants.MessageTypes.TestConnection => true,
                ProcessingConstants.MessageTypes.TestConnectionResponse => true,
                _ => false
            };
        }

        /// <summary>
        /// Validates if an email type is supported
        /// </summary>
        /// <param name="emailType">The email type to validate</param>
        /// <returns>True if the email type is supported, false otherwise</returns>
        public static bool IsSupportedEmailType(string emailType)
        {
            if (string.IsNullOrWhiteSpace(emailType))
                return false;

            return emailType switch
            {
                ProcessingConstants.EmailTypes.Received => true,
                ProcessingConstants.EmailTypes.Sent => true,
                _ => false
            };
        }

        /// <summary>
        /// Sanitizes a filename for safe storage
        /// </summary>
        /// <param name="fileName">The filename to sanitize</param>
        /// <returns>The sanitized filename</returns>
        public static string SanitizeFileName(string fileName)
        {
            if (string.IsNullOrWhiteSpace(fileName))
                return string.Empty;

            // Remove or replace invalid characters
            var invalidChars = Path.GetInvalidFileNameChars();
            var sanitized = fileName;

            foreach (var invalidChar in invalidChars)
            {
                sanitized = sanitized.Replace(invalidChar, '_');
            }

            // Remove leading/trailing spaces and dots
            sanitized = sanitized.Trim(' ', '.');

            // Ensure the filename is not empty after sanitization
            if (string.IsNullOrWhiteSpace(sanitized))
                sanitized = "unnamed_file";

            return sanitized;
        }

        /// <summary>
        /// Validates if a string is a valid base64 encoded data
        /// </summary>
        /// <param name="base64String">The base64 string to validate</param>
        /// <returns>True if the string is valid base64, false otherwise</returns>
        public static bool IsValidBase64String(string base64String)
        {
            if (string.IsNullOrWhiteSpace(base64String))
                return false;

            try
            {
                Convert.FromBase64String(base64String);
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
} 